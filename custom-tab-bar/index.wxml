<!--miniprogram/custom-tab-bar/index.wxml-->
<view
  class="tab-bar"
  style="height: {{tabBarHeight}}rpx;"
>
  <view
    wx:for="{{list}}"
    wx:key="index"
    class="tab-bar-item {{index === selected && 'tab-bar-item-selected'}}"
    data-path="{{item.pagePath}}"
    data-index="{{index}}"
    bindtap="switchTab"
  >
    <view class="float-wrapper" wx:if="{{index === 1}}">
      <view class="text-wrapper">动态贺卡已上线，快来试试吧~</view>
      <image style="width: 100%; height: 100%;" src="../assets/imgs/float-bg.png" mode="aspectFill"/>
    </view>
    <image
      class="tab-bar-icon"
      src="{{index === selected ? item.selectedIcon : item.icon}}"
      mode="aspectFit"
    />
  </view>
</view>
