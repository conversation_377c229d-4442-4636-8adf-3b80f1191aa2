import { getMiniProgramConfig } from '../api/common'
const App = getApp()
Component({
  data: {
    selected: App.globalData.selected,
    list: [
      {
        pagePath: "/pages/home/<USER>",
        icon: "../assets/imgs/home_icon.png",
        selectedIcon: "../assets/imgs/home_icon_active.png",
      },
      {
        pagePath: "",
        icon: "../assets/imgs/e_invite.png",
        selectedIcon: "../assets/imgs/e_invite.png",
      },
      {
        pagePath: "/pages/search/search",
        icon: "../assets/imgs/search_icon.png",
        selectedIcon: "../assets/imgs/search_icon_active.png",
      },
      {
        pagePath: "/pages/collection/collection",
        icon: "../assets/imgs/collection_icon.png",
        selectedIcon: "../assets/imgs/collection_icon_active.png",
      },
    ],
    tabBarHeight: App.globalData.tabBarHeight
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      if (!data.path) {
        getMiniProgramConfig().then(res => {
          if (res.code === '200') {
            const caInfo = wx.getStorageSync('caInfo')
            const adsfId = caInfo.frontUserRole === 'REGION_MANAGER' ? '' : caInfo.employee.adfsId
            const storeId = adsfId ? caInfo.defaultStoreId || caInfo.storeIds[0] : ""
            const query = res.data.query.replace('${id_ADFS__c}', adsfId).replace('${sap_ID__c}', storeId)
            console.log(`${res.data.page}?${query}`, 'path')
            wx.navigateToMiniProgram({
              appId: res.data.appId,
              path: `${res.data.page}?${query}`,
            })
          } else {
            wx.showToast({
              title: res.message,
              icon: 'none'
            })
          }
        })
        return
      }
      if (data.index === this.data.selected) {
        return false;
      }
      wx.switchTab({ url: data.path });
      setTimeout(()=>{
      },1000
      )
    },
    getInstance() {
      if (typeof this.getTabBar === 'function' ) {
        this.getTabBar((tabBar) => {
          tabBar.setData({
            selected: 0
          })
        })
      }
    },
  },
});
