.tab-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    background: #FFFFFF;
    box-shadow: 0rpx -2rpx 10rpx 0rpx rgba(0,0,0,0.1);
  }
  
  .tab-bar-item {
    flex:1;
    height:100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 25rpx;
    position: relative;
  }
  .tab-bar-icon{
    width:56rpx;
    height:56rpx;
  }
  
  .float-wrapper {
    width: 286rpx;
    height: 72rpx;
    position: absolute;
    top: -46rpx;
    
  }
  .float-wrapper .text-wrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9;
    padding: 18rpx 0 28rpx;
    font-weight: 400;
    font-size: 19rpx;
    text-align: center;
    color: #FFFFFF;
    line-height: 26rpx;
  }