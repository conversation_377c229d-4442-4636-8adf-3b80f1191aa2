<van-popup
  wx:if="{{visible}}"
  show="{{ visible }}"
  position="bottom"
  bind:close="close"
>
  <view class="product-popup-detail">
    <block wx:if="{{sizes.length && multipleSize}}">
      <remote-product-sizes
        list="{{sizes}}"
        id="product-size"
        isSize="{{isSize}}"
        selected="{{selected}}"
        bind:close="close"
        bind:select="onChangeSize"
      />
    </block>
    <block wx:else>
      <view
        class="close flex flex-row justify-center items-center"
        bind:tap="close"
      >
        <remote-icon-image name="close" width="{{24}}" height="{{24}}" />
      </view>
      <view class="header">
        <view class="header-container flex flex-row justify-start items-center">
          <view class="header-image-wrapper">
            <image
              class="header-image"
              src="{{url || '../../assets/images/default-pic.jpg'}}"
              mode="aspectFit"
            />
          </view>
          <view class="header-info flex flex-col justify-start">
            <text class="title">{{detail.productName}}</text>
            <text class="desc">{{detail.desc}}</text>
            <text class="price justify-self-start">{{detail.price}}<text wx:if="{{batchManagement}}" class="price-unit">起</text></text>
          </view>
        </view>
      </view>
      <view class="content">
        <remote-product-description-item
          title="作品描述"
          show-toggle="{{false}}"
        >
          <text>{{info.described}}</text>
        </remote-product-description-item>
      </view>
    </block>
    <view
      class="footer flex flex-row justify-between items-center"
      wx:if="{{!loading}}"
    >
      <remote-button
        wx:if="{{batchManagement}}"
        bind:tap="onJumpBatch"
      >
        查看批次
      </remote-button>
      <remote-button wx:else bind:tap="onJumpStore">
        查看库存
      </remote-button>
      <remote-button wx:if="{{!batchManagement}}" type="primary" bind:tap="onAddTray">
        添加到托盘
      </remote-button>
    </view>
  </view>
</van-popup>
