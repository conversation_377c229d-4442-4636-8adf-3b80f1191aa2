@import "../../common";
.product-popup-detail {
  position: relative;
  .close {
    position: absolute;
    top: 10rpx;
    right: 0rpx;
    padding: 30rpx;
  }
  .header {
    padding-inline: 30rpx;
    &-container {
      padding-block: 40rpx;
    }
    &-image {
      width: 240rpx;
      height: 240rpx;
      &-wrapper {
        width: 240rpx;
        height: 240rpx;
        background-color: #f1ede8;
      }
    }
    &-info {
      flex: 1;
      height: 240rpx;
      margin-left: 30rpx;
      .title {
        font-size: 28rpx;
        color: @gary-blank;
        line-height: 28rpx;
      }
      .desc {
        font-size: 24rpx;
        line-height: 24rpx;
        margin-top: 20rpx;
        color: @gary-636676;
      }
      .price {
        font-size: 28rpx;
        color: @gary-blank;
        line-height: 28rpx;
        margin-top: auto;
      }
      .price-unit{
        font-weight: 300;
        font-size: 22rpx;
        color: #636676;
        margin-left: 3rpx;
        line-height: 22rpx;
      }
    }
  }
  .content{
    max-height: 600rpx;
    overflow-y: auto;
    padding-bottom: 40rpx;
  }
  .detail {
    padding-inline: 30rpx;
    gap: 33rpx;
    padding-top: 40rpx;
    &-title {
      font-weight: 500;
      font-size: 26rpx;
      color: @gary-20253B;
      line-height: 26rpx;
    }
    &-desc {
      font-size: 22rpx;
      color: @gary-636676;
      line-height: 36rpx;
      letter-spacing: 1px;
    }
  }
  .footer {
    width: 100%;
    box-sizing: border-box;
    gap: 20rpx;
    padding:20rpx 30rpx;
    overflow: hidden;
  }
}
