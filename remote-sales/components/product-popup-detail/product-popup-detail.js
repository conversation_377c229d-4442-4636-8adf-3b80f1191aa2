import { getProductDetailApi } from "../../api/product";
import initProductDetail from "../../utils/common-handler/product-handler";
import { isVideoMp4URL, isVideoURL, throttle } from "../../utils/utils";
import { navigation } from "../../utils/navigation";
import { storeBindingsBehavior } from "mobx-miniprogram-bindings";
const computedBehavior = require("miniprogram-computed").behavior;
import { cart } from "../../models/cart";
import { globalCache } from "../../utils/cache";

Component({
  behaviors: [storeBindingsBehavior, computedBehavior],
  storeBindings: {
    store: cart,
    actions: ["addCart", "getCartList"],
  },
  data: {
    externalUserId: undefined,
    productId: undefined,
    visible: false,
    loading: true,
    multipleSize: false,
    batchManagement: false,
    sizes: [],
    isSize: true,
    selected: null,
    sizeDetails: [],
    info: {},
    detail: {},

    btnLoading: false,
  },
  lifetimes: {
    attached() {},
  },
  computed: {
    url(data) {
      const urls = data.info.urls || [];
      if (!urls.length) {
        return "";
      }
      const url = urls[0].url || "";

      if (isVideoURL(url)) {
        return "";
      }
      return url ? `${url}?x-oss-process=image/resize,l_340` : "";
    },
  },
  methods: {
    async getProductDetail(id) {
      const { external_userid } = globalCache.get("customerInfo");
      this.setData({ externalUserId: external_userid });
      void wx.showLoading({
        title: "Loading...",
      });
      const res = await getProductDetailApi(id);
      if (res.success) {
        const {
          multipleSize,
          batchManagement,
          sizes,
          isSize,
          sizeDetails,
          detail,
        } = initProductDetail(res.data);
        let info = sizeDetails[0];
        let selected;
        if (multipleSize) {
          const currentSize = sizes.find((item) => item.stock);
          if (currentSize) {
            selected = currentSize;
          }
        }
        this.setData({
          visible: true,
          productId: id,
          multipleSize,
          batchManagement,
          sizes,
          isSize,
          sizeDetails,
          info,
          detail,
          selected,
          loading: false,
        });
      }
      void wx.hideLoading();
    },
    open(id) {
      void this.getProductDetail(id);
    },
    close() {
      this.setData({ visible: false });
    },
    onChangeSize(e) {
      const { item, index } = e.detail;
      this.setData({ selected: item, info: this.data.sizeDetails[index] });
    },

    async onAddTray() {
      if (
        this.data.multipleSize &&
        this.data.sizes.length &&
        !this.data.selected
      ) {
        void wx.showToast({
          title: "请选择尺码",
          icon: "none",
        });
        return;
      }
      await this.addCart({
        batchNumber: "",
        externalUserId: this.data.externalUserId,
        productDetailId: this.data.info.id,
        productId: this.data.productId,
        qty: 1,
        stockWay: null,
        storeSapId: "",
      });
      this.setData({ visible: false });
      this.getCartList();
    },
    /**
     * 查看批次
     * view batch
     */
    onJumpBatch: throttle(function () {
      if (this.data.multipleSize && !this.data.selected) {
        void wx.showToast({
          title: "请选择尺码",
          icon: "none",
        });
        return;
      }
      const query = {
        id: this.data.productId,
      };

      if (this.data.multipleSize && this.data.sizes.length) {
        const selectedSize = this.selectComponent("#product-size").getSelect();
        query.sku = selectedSize.id;
        query.sapCode = selectedSize.sapCode;
      }

      navigation({
        url: "/remote-sales/pages/product-batch/product-batch",
        query,
        success: () => {
          this.setData({
            visible: false,
          });
        },
      });
    }, 500),
    /**
     * 查看库存
     * view inventory
     */
    onJumpStore: throttle(function () {
      const query = {
        id: this.data.productId,
      };
      if (this.data.multipleSize && this.data.sizes.length) {
        const selectedSize = this.selectComponent("#product-size").getSelect();
        if (selectedSize) {
          query.sku = selectedSize.id;
        }
      }

      navigation({
        url: "/remote-sales/pages/product-inventory/product-inventory",
        query,
      });
    }, 500),
    /**
     * 查看托盘
     * view tray
     */
    onJumpTray: throttle(function () {
      navigation({
        url: "/remote-sales/pages/tray/tray",
      });
    }, 500),
  },
});
