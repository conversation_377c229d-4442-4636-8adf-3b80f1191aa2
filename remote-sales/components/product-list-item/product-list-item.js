const computedBehavior = require("miniprogram-computed").behavior;
const { navigation } = require("../../utils/navigation");
const format = require("../../utils/format");
const { storeBindingsBehavior } = require("mobx-miniprogram-bindings");
const { cart } = require("../../models/cart");
const { isVideoURL, isVideoMp4URL } = require("../../utils/utils");

Component({
  behaviors: [storeBindingsBehavior, computedBehavior],
  storeBindings: {
    store: cart,
    fields: ["productMap"],
    actions: ["addCart"],
  },
  properties: {
    data: {
      type: Object,
      value: {},
    },
  },
  data: {
    visible: false,
  },
  computed: {
    priceFormat(data) {
      return format.formatAmount(data.data.amount || 0);
    },
    url(data) {
      const url = data.data.productUrl || "";
      if (isVideoURL(url)) {
        return "";
      }
      return url ? `${url}?x-oss-process=image/resize,l_340` : "";
    },
  },
  methods: {
    onPlusHandler() {
      const { data } = this.data;
      this.selectComponent("#product-popup-detail").open(data.productId);
    },
    onJumpToDetailHandler() {
      const { data } = this.data;
      navigation({
        url: "/remote-sales/pages/product-detail/product-detail",
        query: {
          id: data.productId,
        },
      });
    },
  },
});
