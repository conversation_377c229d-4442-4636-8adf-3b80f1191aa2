@import "../../common";
.custom-popup{
  position: relative;
  padding: 40rpx;
  .header {
    padding: 0 0 40rpx;
    .title {
      font-weight: 400;
      font-size: 30rpx;
      color: #0A1532;
      line-height: 32rpx;
    }
  }
  .close {
    position: absolute;
    top: 40rpx;
    right: 40rpx;
  }
  
  .content{
    max-height: 600rpx;
    overflow-y: auto;
    min-height: 300rpx;
    border-top: 1rpx solid #D7D7D7;
    margin-bottom: 20rpx;
    padding-top: 20rpx;
  }
  .checkbox-list {
    .item {
      padding: 20rpx 0;
      display: flex;
      align-items: center;
      .icon {
        width: 38rpx;
        height: 38rpx;
      }
      .label {
        margin-left: 20rpx;
        font-weight: 400;
        font-size: 26rpx;
        color: #636676;
        line-height: 28rpx;
        letter-spacing: 1px;
      }
    }
  }
  .footer {
    width: 100%;
    box-sizing: border-box;
    gap: 20rpx;
    padding: 0;
    overflow: hidden;
  }
}
