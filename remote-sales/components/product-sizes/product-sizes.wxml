<view class="product-sizes">
  <view class="close flex-row justify-center items-center" bind:tap="onClose">
    <remote-icon-image name="close" width="{{24}}" height="{{24}}" />
  </view>
  <view class="header flex flex-row justify-center items-center">
    <view class="item-text size" wx:if="{{isSize}}">尺寸</view>
    <view class="item-text sap">SAP</view>
  </view>
  <view class="list">
    <view
      wx:for="{{list}}"
      wx:key="unique"
      class="item flex-row justify-center items-center {{index === current ? 'active':''}} {{!item.stock ? 'disabled':''}}"
      data-item="{{item}}"
      data-index="{{index}}"
      bind:tap="onSelectSize"
    >
      <view class="item-text size" wx:if="{{isSize}}">{{item.size}}</view>
      <view class="item-text sap">{{item.sapCode}}</view>
    </view>
  </view>
</view>
