@import "../../common";

.product-sizes {
  padding: 20rpx 30rpx 0;
  position:relative;
  .close{
    position: absolute;
    top:10rpx;
    right:0;
    width:64rpx;
    height:64rpx;
  }

  .header {
    width: 100%;
    padding: 30rpx 0;
    .item-text{
      color:@primary-color;
    }
  }

  .list {
    max-height: 600rpx;
    overflow-y: auto;
    gap:20rpx;
    padding-bottom: 20rpx;
  }

  .item {
    width: 100%;
    height: 100rpx;
    border-radius: 16rpx;

    &.disabled {
      .item-text {
        color: rgba(99, 102, 118, 0.3);
      }
    }
    &.active{
      background-color: @primary-color;
      .item-text {
        color: @gary-white;
      }
    }
  }

  .size {
    width: 234rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #0A1532;
    line-height: 28rpx;
    text-align: center;
  }

  .sap {
    flex: 1;
    font-weight: 500;
    font-size: 28rpx;
    color: #0A1532;
    line-height: 28rpx;
    text-align: center;
  }
}