Component({
  properties: {
    list: {
      type: Array,
      value: [],
    },
    isSize:{
      type: Boolean,
      value: false,
    },
    selected: {
      type: Object,
      value: {},
      observer: function (newVal) {
        if(newVal){
          const index = this.data.list.findIndex(
            (item) => item.id === newVal.id,
          );
          this.setData({ current: index });
        }
      },
    },
  },
  data: {
    current: undefined,
  },
  methods: {
    onClose() {
      this.triggerEvent("close");
    },
    getSelect() {
      const { current, list } = this.data;
      return list[current];
    },
    onSelectSize(e) {
      const { item, index } = e.currentTarget.dataset;
      if (item.stock) {
        this.setData({ current: index });
        this.triggerEvent("select", {
          item,
          index,
        });
      }
    },
  },
});
