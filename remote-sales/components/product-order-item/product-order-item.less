@import "../../common";

.item {
  width: 100%;
  height: 180rpx;
  .image {
    width: 100%;
    height: 100%;
    &-wrapper {
      width: 180rpx;
      height: 180rpx;
      background-color: #F1EDE8;
    }
  }
  .info {
    height: 180rpx;
    margin-left: 32rpx;
    overflow: hidden;
    &-header {
      gap: 10rpx;
      width: 100%;
    }
    .title {
      font-size: 28rpx;
      color: @gary-blank;
      line-height: 32rpx;
      overflow: hidden;
    }
    .qty {
      font-size: 26rpx;
      color: @gary-blank;
      line-height: 28rpx;
      letter-spacing: 2px;
    }
    .desc {
      font-weight: 300;
      font-size: 24rpx;
      color: @gary-636676;
      line-height: 22rpx;
      &:nth-of-type(1) {
        margin-top: 10rpx;
        line-height: 22rpx;
      }
      &:nth-of-type(2) {
        margin-top: 12rpx;
      }
    }
    .price {
      font-size: 26rpx;
      color: @gary-blank;
      line-height: 26rpx;
    }
    .info-footer{
      margin-top: auto;
    }
  }
}
