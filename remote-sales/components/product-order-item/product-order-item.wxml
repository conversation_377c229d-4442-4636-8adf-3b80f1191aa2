<view class="item flex flex-row justify-start">
  <view wx:if="{{checkbox}}" style="margin-right: 20rpx">
    <remote-icon-image
      name="{{checked ? 'checkbox-select':'checkbox'}}"
      width="{{40}}"
      height="{{40}}"
      bind:tap="onCheckedChange"
    />
  </view>
  <view class="image-wrapper">
    <image class="image" mode="aspectFit" src="{{url || '../../assets/images/default-pic.jpg'}}"></image>
  </view>
  <view class="info flex flex-col flex-auto">
    <view class="info-header flex flex-row justify-between items-center">
      <view class="title flex-auto">
        <view class="ellipsis"> {{source.name}} </view>
      </view>
      <text wx:if="{{!checkbox}}" class="qty">x{{source.qty}}</text>
    </view>
    <text
      class="desc"
      wx:if="{{source.materialZhCn || source.watchCaseMaterialZhCn}}"
      >材质：{{source.materialZhCn || source.watchCaseMaterialZhCn}}</text
    >
    <text class="desc" wx:if="{{source.size}}">尺寸：{{source.size}}</text>
    <view class="info-footer flex flex-row justify-between items-center">
      <text class="price">{{source.priceFormat}}</text>
      <remote-stepper
        wx:if="{{checkbox}}"
        value="{{currentValue}}"
        min="{{1}}"
        max="{{source.qty}}"
        bind:num-change="onQtyChange"
      />
      <block wx:else><slot></slot></block>
    </view>
  </view>
</view>
