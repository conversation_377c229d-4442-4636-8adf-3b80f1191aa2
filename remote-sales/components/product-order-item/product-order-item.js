import { behavior as computed<PERSON><PERSON><PERSON><PERSON> } from "miniprogram-computed";
import { isVideoMp4URL, isVideoURL } from "../../utils/utils";

Component({
  behaviors: [computedBehavior],
  options: {
    virtualHost: true,
  },
  properties: {
    checkbox: {
      type: Boolean,
      value: false,
    },
    source: {
      type: Object,
      value: {},
    },
  },
  data: {
    checked: false,
    value: 0,
  },
  observers: {
    'checkbox': function () {
      this.setData({ checked: false, value: 1 });
    }
  },
  computed: {
    currentValue(data) {
      return data.checkbox ? data.value : data.source.qty;
    },
    url(data) {
      const url = data.source.imageUrl || "";
      if (isVideoURL(url)) {
        return "";
      }
      return url ? `${url}?x-oss-process=image/resize,l_340` : "";
    },
  },
  lifetimes: {
    ready() {
      this.setData({ value: this.data.source.qty });
    },
  },

  methods: {
    onCheckedChange() {
      const { checked, source, currentValue } = this.data;
      const _checked = !checked;
      this.setData({ checked: _checked });
      this.triggerEvent("change", {
        checked: _checked,
        orderItemId: source.id,
        productId: source.productId,
        refundQuantity: currentValue,
      });
    },
    onQtyChange(e) {
      const { checked, source } = this.data;
      this.setData({ value: e.detail });
      this.triggerEvent("change", {
        checked: checked,
        orderItemId: source.id,
        productId: source.productId,
        refundQuantity: e.detail,
      });
    },
  },
});
