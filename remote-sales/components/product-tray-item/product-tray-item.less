@import "../../common";

.product-tray-item {
  width: 100%;
  border-bottom: 2rpx solid @gary-E9E9EB;
  padding-bottom: 40rpx;
  margin-top: 40rpx;
  .image {
    width: 100%;
    height: 100%;
    &-wrapper {
      width: 240rpx;
      height: 240rpx;
      background-color: #F1EDE8;
    }
  }
  .info {
    min-height: 240rpx;
    margin-left: 30rpx;
    overflow: hidden;
    position: relative;
    .stock{
      width: 100rpx;
      height: 32rpx;
      background: #F8F6F3;
      font-weight: 300;
      font-size: 20rpx;
      color: #3E362D;
      text-align: center;
      line-height: 32rpx;
      font-style: normal;
      &-wrapper{
        position: absolute;
        right:0;
        top:95rpx;
        gap:10rpx;
      }
    }
    &-header {
      gap: 10rpx;
      width: 100%;
    }
    .title {
      font-size: 28rpx;
      color: @gary-blank;
      line-height: 28rpx;
    }
    .desc {font-weight: 300;
      font-size: 24rpx;
      color: @gary-636676;
      line-height: 22rpx;
      margin-top: 20rpx;
    }
    .size{
      height: 50rpx;
      background: #F3F6F9;
      border-radius: 25rpx;
      padding: 0 20rpx;
      margin-top: 20rpx;
      gap:10rpx;
      &-text{
        font-weight: 300;
        font-size: 24rpx;
        color: @gary-0A1532;
        line-height: 22rpx;
      }
    }
    .price {
      color: @gary-blank;
      font-size: 28rpx;
      line-height: 28rpx;
    }
    .info-footer{
      width:100%;
      margin-top: auto;
    }
  }
}
