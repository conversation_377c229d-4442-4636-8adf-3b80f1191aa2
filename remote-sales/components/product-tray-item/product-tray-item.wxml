<view
  class="product-tray-item flex flex-row justify-start"
  bind:tap="onJumpDetail"
>
  <view class="image-wrapper">
    <image
      class="image"
      src="{{url || '../../assets/images/default-pic.jpg'}}"
      mode="aspectFit"
    ></image>
  </view>
  <view class="info flex flex-col flex-auto items-start">
    <view class="info-header flex flex-row justify-between items-center">
      <view class="title flex-auto ellipsis"> {{source.name}} </view>
      <view>
        <remote-icon-image
          name="delete"
          width="{{30}}"
          height="{{30}}"
          catch:tap="onDelete"
        />
      </view>
    </view>
    <view class="stock-wrapper flex-col">
      <view class="stock" wx:if="{{source.offlineInventory > 0}}">店铺库存</view>
      <view class="stock" wx:if="{{source.onlineInventory > 0}}">大仓库存</view>
    </view>
    <text
      class="desc"
      wx:if="{{source.materialZhCn || source.watchCaseMaterialZhCn}}"
      >材质：{{source.materialZhCn || source.watchCaseMaterialZhCn}}</text
    >
    <view
      class="desc flex-row justify-center items-center"
      wx:if="{{source.size}}"
    >
      尺寸：{{source.size}}
    </view>
    <view
      class="desc flex-row justify-center items-center"
      wx:if="{{source.sapCode || source.batchNumber}}"
    >
      编号：{{source.batchManagement === 'Yes' ? source.sapCode + ' | ' +
      source.batchNumber : source.sapCode}}
    </view>
    <view class="desc flex-row justify-center items-center" wx:if="{{names}}">
      批次：{{names}}
    </view>
    <view class="info-footer flex flex-row justify-between items-center">
      <text class="price">{{source.priceFormat}}</text>
      <remote-stepper
        value="{{source.qty}}"
        min="{{0}}"
        max="{{3}}"
        loading="{{qtyLoading}}"
        bind:num-change="onQtyChange"
      />
    </view>
  </view>
</view>
