const computedBehavior = require("miniprogram-computed").behavior;
import { storeBindingsBehavior } from "mobx-miniprogram-bindings";
import { cart } from "../../models/cart";
import { isVideoMp4URL, isVideoURL, throttle } from "../../utils/utils";
import { navigation } from "../../utils/navigation";

Component({
  behaviors: [storeBindingsBehavior, computedBehavior],
  storeBindings: {
    store: cart,
    actions: ["deleteCart", "updateCartNum", "getCartList"],
  },
  options: {
    virtualHost: true,
  },
  properties: {
    source: {
      type: Object,
      value: {},
    },
    checkbox: {
      type: Boolean,
      value: false,
    },
    checked: {
      type: Boolean,
      value: false,
    },
  },
  computed: {
    names(data) {
      const names = [
        data.source.caratWeight,
        data.source.color,
        data.source.clarity,
      ].filter(Boolean);
      return names.join(" ");
    },

    url(data) {
      const url = data.source.imageUrl || "";
      if (isVideoURL(url)) {
        return "";
      }
      return url ? `${url}?x-oss-process=image/resize,l_340` : "";
    },
  },
  data: {
    qtyLoading: false,
    deleteLoading: false,
  },
  methods: {
    onJumpDetail() {
      const { source } = this.data;
      navigation({
        url: "/remote-sales/pages/product-detail/product-detail",
        query: {
          id: source.productId,
        },
      });
    },
    onDelete: throttle(function () {
      if (this.data.deleteLoading) return;
      wx.showModal({
        title: "提示",
        content: "确定要从托盘移除该商品吗？",
        success: async (res) => {
          if (res.confirm) {
            this.setData({ deleteLoading: true });
            await this.deleteCart(this.data.source.id);
            await this.getCartList();
            this.setData({ deleteLoading: false });
          }
        },
      });
    }, 500),
    onQtyChange: throttle(async function (e) {
      if (e.detail === 0) {
        this.onDelete();
      } else {
        this.setData({ qtyLoading: true });
        try {
          await this.updateCartNum(this.data.source.id, e.detail, false);
          await this.getCartList();
        } catch (error) {
          console.log(error);
        }
        this.setData({ qtyLoading: false });
      }
    }, 500),
  },
});
