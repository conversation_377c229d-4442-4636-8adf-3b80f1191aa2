<van-popup
  z-index="{{99999}}"
  show="{{show}}"
  position="bottom"
  bind:close="close"
>
  <remote-popup-header title="选择退款理由" bind:close="close" />
  <view class="reasons-refunds">
    <view class="content flex-col">
      <view class="item" wx:for="{{sourceList}}" wx:key="unique">
        <view
          class="item-header flex-row justify-between items-center"
          data-key="{{item.key}}"
          catch:tap="onToggleOpen"
        >
          <view
            class="flex-row justify-start items-center"
            style="gap: 20rpx"
            data-item="{{item}}"
          >
            <remote-icon-image
              name="{{item.selected ? 'checkbox-select' : 'checkbox'}}"
              width="{{40}}"
              height="{{40}}"
            />
            <text class="title">{{item.title}}</text>
          </view>
          <remote-icon-image
            name="arrow-down"
            rotate="{{item.open ? '180deg' : '0deg'}}"
            width="{{22}}"
            height="{{22}}"
          />
        </view>
        <view class="item-list flex-col" wx:if="{{item.open}}">
          <view
            class="flex-row justify-start items-center"
            wx:for="{{item.children}}"
            wx:for-item="child"
            wx:key="unique"
            style="gap: 18rpx"
            data-item="{{child}}"
            data-parent="{{item.id}}"
            bind:tap="onToggleSelect"
          >
            <remote-icon-image
              name="{{child.key === selectKey ? 'checkbox-select' : 'checkbox'}}"
              width="{{40}}"
              height="{{40}}"
            />
            <text class="sub-title">{{child.title}}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="footer flex-row">
      <remote-button bind:tap="onReset">重置</remote-button>
      <remote-button type="primary" bind:tap="onConfirm">提交</remote-button>
    </view>
  </view>
</van-popup>
