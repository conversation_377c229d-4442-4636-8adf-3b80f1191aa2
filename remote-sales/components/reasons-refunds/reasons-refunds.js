import { behavior as computed<PERSON><PERSON>avior } from "miniprogram-computed";
import { getRefundReasonListApi } from "../../api/order";

function listToTree(list, id, pid) {
  const map = {};
  const tree = [];
  list.forEach((item) => {
    map[item[id]] = item;
  });
  list.forEach((item) => {
    const parent = map[item[pid]];
    if (parent) {
      (parent.children || (parent.children = [])).push(item);
    } else {
      tree.push(item);
    }
  });
  return tree;
}

Component({
  behaviors: [computedBehavior],
  data: {
    show: false,
    list: [],
    originList: [],
    openKeys: [],
    parentSelect: undefined,
    selectKey: undefined,
    selectTitle: undefined,
  },
  computed: {
    sourceList(data) {
      return data.list.map((item) => {
        const open = data.openKeys.includes(item.key);
        let selected = false;
        if(item.children && item.children.length){
          item.children.forEach((_child) => {
            if (_child.id === data.selectKey) {
              selected = true;
            }
          });
        }
        return { ...item, open, selected };
      });
    },
  },
  lifetimes: {
    attached() {},
  },
  methods: {
    /**
     * 获取退款理由
     * get refund reason
     */
    async fetchRefundReasonList() {
      const { originList, selectTitle } = this.data;
      if (originList.length) {
        if (selectTitle) {
          let selectedKey = null;
          let parentSelect = null;
          originList.forEach((item) => {
            if (item.title === selectTitle) {
              selectedKey = item.id;
              parentSelect = item.parentId;
            }
          });
          this.setData({
            selectKey: selectedKey,
            parentSelect: parentSelect,
            openKeys: [parentSelect],
          });
        }
        return;
      }
      const result = await getRefundReasonListApi();
      if (result.success) {
        let selectedKey = null;
        let parentSelect = null;
        const _sortList = (result.data || []).sort((a,b)=>a.parentId - b.parentId).sort((a, b)=>a.sort - b.sort)
        const list = _sortList.map((item) => {
          if (selectTitle && item.reasonCn === selectTitle) {
            selectedKey = item.id;
            parentSelect = item.parentId;
          }
          return {
            id: item.id,
            parentId: item.parentId,
            key: item.id,
            title: item.reasonCn,
          };
        });
        const _data = {
          originList: list,
          list: listToTree(list, "id", "parentId"),
        };
        if (selectedKey) {
          _data.selectKey = selectedKey;
          _data.parentSelect = parentSelect;
          _data.openKeys = [parentSelect];
        }
        this.setData(_data);
      }
    },
    onToggleOpen(e) {
      const { key } = e.currentTarget.dataset;
      const { openKeys } = this.data;
      const index = openKeys.indexOf(key);
      if (index === -1) openKeys.push(key);
      else openKeys.splice(index, 1);
      this.setData({ openKeys });
    },
    onToggleSelect(e) {
      const { item, parent } = e.currentTarget.dataset;
      this.setData({
        selectKey: item.key,
        selectTitle: item.title,
        parentSelect: parent,
      });
    },
    open(title) {
      this.onReset();
      this.setData({ show: true, selectTitle: title });
      void this.fetchRefundReasonList();
    },
    close() {
      this.setData({ show: false });
    },
    onReset() {
      this.setData({
        openKeys: [],
        parentSelect: null,
        selectKey: null,
        selectTitle: "",
      });
    },
    onConfirm() {
      const { selectKey, selectTitle } = this.data;
      if (!selectKey) {
        void wx.showToast({
          title: "请选择退款理由",
          icon: "none",
        });
        return;
      }
      this.close();
      this.triggerEvent("confirm", selectTitle);
    },
  },
});
