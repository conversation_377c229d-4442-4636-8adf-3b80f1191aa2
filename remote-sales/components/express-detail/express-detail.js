import { getCustomerOrderTrackPayApi, getOrderTrackApi } from "../../api/order";
import { isWxWorkEnv } from "../../utils/is";
Component({
  properties: {},
  data: {
    show: false,
    trackNumber: "",
    trackList: [],
  },
  methods: {
    init(orderNo) {
      wx.showLoading({
        title: "加载中...",
      });
      const requestFn = isWxWorkEnv()
        ? getOrderTrackApi
        : getCustomerOrderTrackPayApi;
      requestFn(orderNo)
        .then((res) => {
          this.setData({
            trackNumber: res.data.trackNumber,
            trackList: (res.data.trackinfo || []).reverse(),
          });
        })
        .finally(() => {
          wx.hideLoading();
        });
    },
    onCopy() {
      wx.setClipboardData({
        data: this.data.trackNumber,
        success() {
          void wx.showToast({
            title: "复制成功",
            icon: "none",
          });
        },
      });
    },
    open(orderNo) {
      this.setData({ show: true });
      this.init(orderNo);
    },
    onClose() {
      this.setData({ show: false, trackNumber: "", trackList: [] });
    },
  },
});
