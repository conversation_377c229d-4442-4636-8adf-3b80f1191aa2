<!--components/materialTypes/index.wxml-->
<view class="material_types flex align-center" wx:if="{{types.length>0}}">
  <view class="material_types_item {{item.type == activeType ? 'material_types_item_active':''}}" wx:for="{{types}}" wx:key="index" bindtap="handleClickType" data-type="{{item.type}}">
    <image class="material_types_item_image" src="../../assets/imgs/{{item.type == activeType ? item.activeIcon : item.icon}}" mode="widthFix"></image>
    <text class="material_types_item_text">{{ item.name }}</text>
  </view>
</view>

