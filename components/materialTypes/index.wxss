/* components/materialTypes/index.wxss */
.material_types{
  border: 1px solid rgba(217, 217, 217, 1);
  border-radius: 10rpx;
  overflow: hidden;
}
.material_types_item{
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  min-width: 130rpx;
  padding: 0 10rpx;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: 65rpx;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-right: 1px solid rgba(217, 217, 217, 1);
  background: #fff;
}

.material_types_item:last-child{
  border-right: none;
}
.material_types_item_image{
  width: 22rpx;
  height: auto;
  margin-right: 10rpx;
}
.material_types_item_text{
  color: rgba(99, 102, 118, 1);
  font-size: 12px;
  white-space: nowrap;
  font-family: PingFangSC, PingFang SC;
}
.material_types_item_active{
  background: rgba(219, 138, 6, 1);
}
.material_types_item_active .material_types_item_text{
  color: #fff;
}
.material_types_text{
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
}