const App = getApp()

Component({
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },
  properties: {
    activeType: String,
    total: Number,
    materialSource: {
      type: Number,
      observer: function(nv){
        if(nv === 1){
          this.setData({
            types: [],
          })
        }else{
          this.setData({
            types: [{
              name: '图片',
              nameEn: 'Image',
              language: 'image',
              icon: 'image_icon.png',
              activeIcon: 'image_icon_active.png',
              type: 0
            },{
              name: '视频',
              nameEn: 'Video',
              language: 'video',
              icon: 'video_icon.png',
              activeIcon: 'video_icon_active.png',
              type: 1
            },{
              name: '外链',
              nameEn: 'Link',
              language: 'link',
              icon: 'link_icon.png',
              activeIcon: 'link_icon_active.png',
              type: 2
            }],
          })
        }
      }
    },
  },
  data: {
    types: [{
      name: '图片',
      nameEn: 'Image',
      language: 'image',
      icon: 'image_icon.png',
      activeIcon: 'image_icon_active.png',
      type: 0
    },{
      name: '视频',
      nameEn: 'Video',
      language: 'video',
      icon: 'video_icon.png',
      activeIcon: 'video_icon_active.png',
      type: 1
    }
    ,{
      name: '外链',
      nameEn: 'Link',
      language: 'link',
      icon: 'link_icon.png',
      activeIcon: 'link_icon_active.png',
      type: 2
    }
  ],
  },
  lifetimes: {
    attached: function () {
    }
  },
  methods: {
    handleClickType(e){
      let type = e.currentTarget.dataset.type
      this.triggerEvent('typeClick', type);
    }
  }
});