<!--components/contentPreview/contentPreview.wxml-->
<wxs src="../../app.wxs" module="tools" />
<view class="content_preview">
			<view class="content_box">
				<view class="content_logo"><image src="../../assets/imgs/logo.png" class="navbar_logo" mode="widthFix"></image></view>
				<view style="overflow: hidden; flex: 1;">
          <scroll-view scroll-y="{{true}}" class="scroll-box" style="height: 100%" enhanced
  show-scrollbar="{{false}}">
            <view style="padding: 0 42rpx;" wx:if="{{show}}">
              <view class="content_imgs" >
                <view class="more">{{'<'}} >滑动查看更多</view>
                <swiper indicator-dots="{{false}}"	indicator-width="{{60}}"
                  indicator-active-color="#62605E"
                  autoplay="{{false}}" circular current="{{current}}" bindchange="classChange" >
                  <block wx:for="{{dataList}}" wx:key="id">
                    <swiper-item>
                      <image src="{{item.type == 2?item.videoPictureLink:item.urlFile}}" class="navbar_logo" mode="aspectFit" bindtap="preview" data-index="{{index}}" ></image>
                    </swiper-item>
                  </block>
                </swiper>

                <view class="indicatorBox">
                  <view wx:for="{{dataList}}" style="width:{{indicatorWidth}}%" class="{{index == current ? 'selected' : 'normal'}}"  wx:key="index" data-index="{{index}}" ></view>
                </view>
              </view>
            
              <view class="content_title">
                <text wx:if='{{itemDetailData.aestheticMotif == "Divas\' Dream"}}'>Divas' Dream</text>
                <text wx:else>{{itemDetailData.aestheticLine}}</text>
                <text>{{itemDetailData.familyName === null ? '' : itemDetailData.familyName}}</text>
              </view>
              <view class="content_code">{{itemDetailData.catalogCode}}</view>
              <view class="content_amount" wx:if="{{itemDetailData.amount}}">
                ¥ {{tools.formatPrice(itemDetailData.amount)}}{{itemDetailData.batchManagement === 'Yes'?'起':''}}
              </view>
              <view wx:if="{{tagList.length}}">
                <view class="content_tags">
                <view class="content_tag" wx:for="{{tagList}}" wx:key="item" wx:if="{{index < tagLength}}">{{item}}</view>
              </view>
              <view class="content_tagAll" wx:if="{{tagList.length > tagLength}}" bindtap="tagAll">
                查看全部
              </view>
              <view class="content_tagAll" wx:else bindtap="tagHiddenAll">
                隐藏
              </view>
              </view>
              <block wx:if="{{itemDetailData.marketingLongDescriptionZhCN}}">
                <view class="content_h3" bindtap="toggleDescribe">
                  <text>作品描述</text>
                  <text >{{describeMore?'-':'+'}}</text>
                </view>
                <view class="content_text" wx:if="{{describeMore}}">
                  <rich-text nodes="{{itemDetailData.marketingLongDescriptionZhCN}}"></rich-text>
                </view>
              </block>
              <block wx:if="{{itemDetailData.materialZhCN || itemDetailData.colorZhCN || itemDetailData.marketingMainStoneZhCN}}">
                <view class="content_h3" bindtap="toggleDetails">
                  <text>细节</text>
                  <text >{{detailsMore?'-':'+'}}</text>
                </view>
                <view class="content_text_list" wx:if="{{detailsMore}}">
                  <view class="li" wx:if="{{itemDetailData.materialZhCN}}">材质：{{itemDetailData.materialZhCN}}</view>
                  <view class="li" wx:if="{{itemDetailData.colorZhCN}}">颜色：{{itemDetailData.colorZhCN}}</view>
                  <view class="li" wx:if="{{itemDetailData.marketingMainStoneZhCN}}">宝石：{{itemDetailData.marketingMainStoneZhCN}}</view>
                </view>
              </block>
            </view>
          </scroll-view>
        </view>
        
			</view>
			<view class="horn horn_top_left"></view>
			<view class="horn horn_top_right"></view>
			<view class="horn horn_bottom_left"></view>
			<view class="horn horn_bottom_right"></view>
		</view>