/* components/contentPreview/contentPreview.wxss */

.content_preview {
	width: 100%;
	height: 100%;
	background: #f2f2f2;
	padding: 43rpx;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	position: relative;
}
.horn {
	width: 90rpx;
	height: 90rpx;
	background: red;
	background: #f2f2f2;
	border-radius: 50%;
	position: absolute;
}
.horn_top_left {
	left: 0;
	top: 0;
}
.horn_top_right {
	right: 0;
	top: 0;
}
.horn_bottom_left {
	left: 0;
	bottom: 0;
}
.horn_bottom_right {
	right: 0;
	bottom: 0;
}

.content_box {
	background: #ffffff;
	padding: 65rpx 0;
	height: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	font-family: PingFangSC, PingFang SC;
	font-weight: 300;
	font-size: 24rpx;
  color: #636676;
  display: flex;
  flex-direction: column;
}
.scroll-box{
  width: auto;
}
.scroll-box::-webkit-scrollbar {
  display: none;
  width:0;
  height:0;
  color:transparent;
}
.content_logo {
	width: 250rpx;
	height: 37rpx;
	margin: 0 auto 40rpx;
}
.content_logo image {
	width: 100%;
}
.content_imgs {
	width: 100%;
	background: #f1ede8;
	position: relative;
}
.content_imgs image {
	width: 100%;
	height: 100%;
}
.content_imgs swiper {
	height: 100% ;
}

.content_imgs::before {
	content: "";
	display: block;
	padding-top: 100%;
}
.content_imgs > swiper {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
}
.more {
	position: absolute;
	left: 0;
	bottom: 20rpx;
	width: 100%;
	text-align: center;
	font-weight: 300;
	font-size: 20rpx;
	color: #636676;
  z-index:10;
}
.indicatorBox{
  width: 100%;
  position: absolute;
	left: 0;
	bottom: 0;
  height: 2rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.indicatorBox > view{
  width: 31.3%;
	border-radius: 5rpx;
	height: 2rpx;
}

.indicatorBox > .selected{
  background: #62605E;
}

.content_title {
	font-weight: 400;
	font-size: 46rpx;
  color: #20253b;
  margin-top: 40rpx;
  line-height: 1;
}
.content_code {
	font-weight: 300;
	font-size: 24rpx;
	color: #636676;
	line-height: 24rpx;
	letter-spacing: 1px;
	margin-top: 20rpx;
}
.content_amount{
  font-family: AvenirNext, AvenirNext;
  font-weight: 500;
  font-size: 36rpx;
  color: #0A1532;
  line-height: 49rpx;
  letter-spacing: 1px;
  margin-top: 16rpx;
}
.content_tags {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	margin-top: 30rpx;
}
.content_tag {
	height: 42rpx;
  min-width: 11rpx;
  text-align: center;
	background: #f1ede8;
	border-radius: 21rpx;
	backdrop-filter: blur(10px);
	font-weight: 400;
	font-size: 20rpx;
	color: #db8a06;
	padding: 0 16rpx;
	line-height: 42rpx;
	margin-right: 10rpx;
	margin-bottom: 10rpx;
}
.content_tagAll{
  font-weight: 300;
  font-size: 22rpx;
  color: #636676;
  line-height: 30rpx;
  text-decoration-line: underline;
  margin-top: 6rpx;
}
.content_h3 {
	font-weight: 500;
	font-size: 26rpx;
  line-height: 1;
	color: #20253b;
  margin-top: 38rpx;
  padding: 20rpx 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
  cursor: pointer;
  margin-right: -40rpx;
  padding-right: 40rpx;
}
.content_text {
	line-height: 39rpx;
}
.content_text_list {
	line-height: 39rpx;
}

