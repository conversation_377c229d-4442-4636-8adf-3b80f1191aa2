// components/contentPreview/contentPreview.js
const App = getApp();
import { queryProductById } from '../../api/contentlibrary'
Component({
	/**
	 * 组件的属性列表
	 */
	properties: {
		show:  {
			type: <PERSON><PERSON><PERSON>,
			observer(newVal, oldVal) {
        if (newVal) {
          this.setData({
            current: 0,
          });
        }
			},
		},
		contentHeight: {
			type: Number,
			value: 690,
		},
    previewId: {
			type: String,
			observer(newVal, oldVal) {
        if (newVal) {
          this.setData({
            previewId: newVal,
            current: 0
          });
          this.getProductDetail(newVal);
        }
			},
		},
    itemData: {
			type: Object,
			observer(newVal, oldVal) {
        if (newVal) {
          const tags = [];
          const properties = [newVal.genderZhCN, newVal.hjReproducibility, newVal.colorZhCN, newVal.materialZhCN, newVal.marketingMainStoneZhCN, newVal.catalogCode, newVal.aestheticMotif, newVal.aestheticLine, newVal.business];
          properties.forEach(property => {
            if (property) {
              tags.push(property);
            }
          });
          const width = newVal.externalFiles?100/newVal.externalFiles.length:0;
          this.setData({
            itemDetailData:newVal,
            dataList: newVal.externalFiles,
            indicatorWidth: width,
          });
        }
			},
    },
	},

	/**
	 * 组件的初始数据
	 */
	data: {
		describeMore: true,
		detailsMore: true,
    itemDetailData:{},
    dataList:[],
    tagList:[],
    tagLength:3,
    current: 0,
    indicatorWidth: 20,
	},

	/**
	 * 组件的方法列表
	 */
	methods: {
    async getProductDetail(id){
      let params = {
        productId: id,
        productType: 1,
      };
      const result = await queryProductById(params);
      if (result.code == 200) {
        const newVal = result.data;
        if (newVal) {
          const tags = [];
          const properties = [newVal.genderZhCN, newVal.hjReproducibility, newVal.colorZhCN, newVal.materialZhCN, newVal.marketingMainStoneZhCN, newVal.aestheticMotif, newVal.aestheticLine, newVal.business];
          properties.forEach(property => {
            if (property) {
              tags.push(property);
            }
          });
          const width = newVal.externalFiles?100/newVal.externalFiles.length:100;
          this.setData({
            itemDetailData:newVal,
            dataList: newVal.externalFiles,
            tagList: tags,
            indicatorWidth: width,
          });
        }
      }
    },
    tagAll(){
      this.setData({
        tagLength: 99
      });
    },
    tagHiddenAll(){
      this.setData({
        tagLength: 3
      });
    },
    classChange(e) {
      this.setData({
				current: e.detail.current,
			});
    },
    preview(e){
      const index = e.currentTarget.dataset.index;
      const list = this.data.dataList?.map((item)=>{
        return item.urlFile;
      })
      const typeList = this.data.dataList?.map((item)=>{
        return item.type;
      })
      wx.navigateTo({
        url: `/pages/imgPrevirw/imgPrevirw?current=${index}&urls=${list.toString()}&types=${typeList.toString()}`});
    },
		toggleDescribe() {
			this.setData({
				describeMore: !this.data.describeMore,
			});
		},
		toggleDetails() {
			this.setData({
				detailsMore: !this.data.detailsMore,
			});
		},
	},
});
