const App = getApp()
import {disCollectProduct,collectProduct, disCollectExtProduct, collectExtProduct } from '../../../api/contentlibrary'
Component({
  options: {
    multipleSlots: true
  },
  properties: {
    data: {
      type: Object,
      value: {},
      observer: function (nv, ov) {
        if (nv) {
          this.setData({
            collection: nv.hasCollect || nv.hasCollect ? true: false
          })
        }
      }
    },
    selected: {
      type: Array
    },
    playId: {
      type: String,
      observer: function (nv, ov) {
        if (nv != 'null' && nv == this.data.data.id) {
          this.setData({
            showVideo: true
          })
        } else {
          this.setData({
            showVideo: false
          })
        }
        this.setData({
          currentTime: 0
        })
      }
    },
    source: String,
    isSys: Boolean
  },

  data: {
    showVideo: false,
    collection: false,
    currentTime: 0,
    fullScreen: false
  },
  lifetimes: {
    attached: function () {
      this.setData({
        entry: App.globalData.entry
      })
    }
  },

  methods: {
    bindfullscreenchange(e){
      this.data.fullScreen = e.detail.fullScreen
      this.setData({
        showVideo: this.data.fullScreen ? true : false,
        currentTime: this.data.currentTime
      })
    },
    bindtimeupdate(e){
      this.data.currentTime = e.detail.currentTime
    },
    bindended() {
      this.setData({
        showVideo: false,
        currentTime: 0
      })
    },
    handlePause() {
      this.setData({
        showVideo: this.data.fullScreen ? true : false,
        currentTime: this.data.currentTime
      })
    },
    playVideo() {
      this.setData({
        showVideo: true
      })
      this.triggerEvent('play', this.data.data)
    },
    async handleCollection(event){
			const { id, hascollect } = event.currentTarget.dataset;
      const params = {
        productId: id
      }
      let result;
      if(this.data.isSys){
        result = hascollect ? await disCollectProduct(params) : await collectProduct(params)
      }else{
        result = hascollect ? await disCollectExtProduct(params) : await collectExtProduct(params)
      }
			if (result.code == 200) {
        this.setData({
          collection: hascollect ? false : true
        })
        if(this.data.source == 'collection' && !this.data.collection){
          App.toast('已取消收藏')
        }
      }else{
        App.toast('收藏失败，请重试', 'error')
      }
    },
    handleCheckbox() {
      this.triggerEvent('checkbox', this.data.data)
    }
  }
})