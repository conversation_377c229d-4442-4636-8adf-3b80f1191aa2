<!--components/material/video-item/index.wxml-->
<wxs src="./index.wxs" module="tools" /> 
<view class="picture_item" style="height:{{data.height}}px" >
  <block wx:if="{{showVideo}}" >
    <video style="height:{{data.height}}px" class="video_item" bindended="bindended" bindpause="handlePause" autoplay="{{true}}" object-fit="contain" src="{{data.productUrl}}" play-btn-position="center" muted show-mute-btn controls="{{true}}" poster="{{data.videoPictureLink}}" show-play-btn="{{true}}" bindtimeupdate="bindtimeupdate" 	initial-time="{{currentTime}}" bindfullscreenchange="bindfullscreenchange"></video>
    <!-- <image mode="widthFix" src="../../../assets/imgs/pause.png" class="play_icon" bindtap="handlePause"></image> -->
  </block>
  
  <block wx:if="{{!showVideo}}">
    <image mode="widthFix" src="../../../assets/imgs/play.png" class="play_icon" bindtap="playVideo"></image>
    <!-- <image class="picture_item_image" style="height:{{data.height}}px" src="{{data.videoPictureLink}}" mode="aspectFill" lazy-load></image> -->
    
    <van-image use-loading-slot  class="picture_item_image" style="height:{{data.height}}px" src="{{data.videoPictureLink}}"  lazy-load fit="cover">
      <van-loading wx:if="{{data.videoPictureLink}}" slot="loading" type="spinner" size="20" vertical />
    </van-image>
  </block>
  <view class="picture_item_content {{tools.fn(selected, data.materialId) > 0 ? 'picture_item_content_active' : ''}}" wx:if="{{!showVideo}}">
    <view class="picture_item_content_top flex align-center justify-between">
      <view class="picture_item_t_left" bindtap="handleCheckbox">
          <image mode="widthFix" src="../../../assets/imgs/{{tools.fn(selected, data.id) > 0 ? 'checkbox_active.png' : 'checkbox.png'}}"></image>
      </view>
      <!-- <view wx:else></view> -->
      <view class="picture_item_t_center" >
          <view class="picture_item_t_new" wx:if="{{data.isNew}}">new</view>
      </view>  
      <view class="picture_item_t_right" bindtap="handleCollection"  data-id="{{data.id}}" data-hasCollect="{{collection}}">
        <image src="../../../assets/imgs/{{collection ? 'collection_active.png' : 'collection.png'}}" mode="widthFix"></image>
      </view>
    </view>
    <!-- <view class="picture_item_content_tags" wx:if="{{data.tags && data.tags.length}}">
      <tag class="picture_item_content_tags_item" wx:for="{{data.tags}}" value="{{item}}" />
    </view> -->
  </view>

</view>