/* components/material/video-item/index.wxss */
@import '../../../app.wxss';
.picture_item_image{
  width: 100%;
  height: auto;
  vertical-align: bottom;
}
.picture_item{
  position: relative;
  margin-bottom: 10rpx;
  width: 100%;
}
.play_icon{
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  z-index: 1;
}
.picture_item_content{
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  padding: 20rpx;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
/* .picture_item_content_top{
  position: absolute;
  left: 20rpx;
  top: 20rpx;
  right: 20rpx;
}
.picture_item_content_tags{
  position: absolute;
  left: 20rpx;
  bottom: 20rpx;
  right: 20rpx;
} */
.picture_item_content_active{
  background: rgba(0,0,0,0.5);
}
.picture_item_content_top image{
  width: 40rpx;
  height: auto;
}
.picture_item_content_tags{
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.picture_item_content_tags_item{
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}
.video_item{
  width: 100%;
  height: auto;
}
.picture_item_t_left_active {
  background: rgba(219, 138, 6, 1);
  border-radius: 50%;
  width: 38rpx;
  height: 38rpx;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  line-height: normal;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: #000;
  font-size: 13px;
  border: 4rpx solid #fff;
  font-weight: bolder;
}
.picture_item_t_center{
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.picture_item_t_new{
  width: 60rpx;
  height: 30rpx;
  background: rgba(10,21,50,0.7);
  backdrop-filter: blur(10px);
  border-radius: 3rpx;
  text-align: center;
  line-height: 30rpx;
  font-weight: 400;
  font-size: 22rpx;
  color: #FFFFFF;
  margin: -10rpx 30rpx 0;
  flex-shrink: 0;
}
.picture_item_t_left, .picture_item_t_right{
  padding: 20rpx;
  margin: -20rpx;
}