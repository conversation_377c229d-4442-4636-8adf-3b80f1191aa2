<!--components/material/link/index.wxml-->
<scroll-view class="link-page-wrapper" scroll-y="{{true}}" lower-threshold="{{200}}" enable-flex="{{false}}"  bindscrolltolower="scrolltolower">
  <view class="link-item-box" wx:for="{{newList}}" wx:key="index">
    <linkItem data="{{item}}" bind:checkbox="handleCheckbox" selected="{{selected}}" source="{{source}}" bind:collection="handleCollection" bind:preview="handlePreview"  isSys="{{materialSource === 0}}"/>
  </view>
</scroll-view>

<page-meta page-style="{{ showPreview ? 'overflow: hidden;' : '' }}" />

<!-- <shareBar wx:if="{{selected}}" selected="{{[selected]}}" selectItem="{{[selectItem]}}"  msgtype="news" bind:complete="handleComplete"/> -->


