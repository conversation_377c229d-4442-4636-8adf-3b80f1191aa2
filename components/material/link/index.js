const App = getApp();

Component({
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },
  /**
   * 组件的属性列表
   */
  properties: {
    list: {
      type: Array,
      value: [],
      observer: function(n, o){
        this.setData({
          newList: this.data.list
        })
      }
    },
    source: String,
    activeBar: {
      type: Object,
      observer: function(nv){
      }
    },
    materialSource: Number,
    clearSelected: {
      type: <PERSON><PERSON><PERSON>,
      observer: function(v){
        if(v === true){
          this.setData({
            selected: null,
            selectItem: null
          })
        }
      }
    },
    selectedList: {
      value: Array,
      value: [],
      observer: function(nv, ov){
        if (JSON.stringify(nv) === JSON.stringify(ov)) {return}
        const selectedIds = nv.map(item => item.id)
        this.setData({
          selected: selectedIds,
          selectItem: nv
        })
      }
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    selected: null,
    selectItem: null,
    newList: [],
    showPreview: false
  },
  lifetimes: {
    attached: function () {
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    handleCheckbox(e) {
      let id = e.detail.id
      if (id == this.data.selected) {
        this.setData({
          selected: null,
          selectItem: null
        })
        this.triggerEvent('select', {
          selected: [],
          selectItem: [],
          msgtype: 'news',
          msgtypeNum: 3
        })
      } else {
        this.setData({
          selected: id,
          selectItem: e.detail
        })
        this.triggerEvent('select', {
          selected: [id],
          selectItem: [e.detail],
          msgtype: 'news',
          msgtypeNum: 3
        })
      }
    },
    handleCollection(e){
      let index = this.data.newList.findIndex(v => v.id == e.detail.id)
      this.data.newList.splice(index, 1)
      this.setData({
        newList: this.data.newList
      })
      if(!this.data.newList.length){
        this.triggerEvent('refresh')
      }
    },
    scrolltolower(e){
      this.triggerEvent('loadmore')
    },
    handlePreview(e){
      let data = e.detail
      this.setData({
        showPreview: true,
        previewData: data
      })
      this.triggerEvent('preview', data)
    },
    closePreview() {
      this.setData({
        showPreview: false
      })
    },
    setClipboard() {
      wx.setClipboardData({
        data: this.data.previewData.link,
        success: function (res) {
          wx.showToast({
            title: '复制成功',
            icon: 'none'
          });
        },
        fail: function (res) {
          wx.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      })
    },
    handleComplete(){
      this.setData({
        selected: null,
        selectItem: null
      })
    }
  }

})