/* components/material/picture/index.wxss */
.waterfall-page-wrapper {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  /* display: flex;
  flex-wrap: wrap; */
}
.waterfall-item-box{
  margin-bottom: 10rpx;
  width: 50%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: inline-block
}
.waterfall-item-box:nth-of-type(2n){
  padding-left: 5rpx;
}
.waterfall-item-box:nth-of-type(2n + 1){
  padding-right: 5rpx;
}