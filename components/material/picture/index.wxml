<!--components/material/picture/index.wxml-->
<scroll-view class="waterfall-page-wrapper" scroll-y="{{true}}" lower-threshold="{{200}}" enable-flex="{{true}}" bindscrolltolower="scrolltolower">
  <view style="padding: 0 10rpx;">
    <view class="waterfall-item-box" wx:for="{{newList}}" wx:key="index">
      <pictureItem selected="{{selected}}" data="{{item}}" source="{{source}}" bind:checkbox="handleCheckbox" bind:collection="handleCollection" bind:preview="handlePreview" isSys="{{materialSource === 0}}" materialSource="{{materialSource}}" bind:updatePreviewShow="updatePreviewShow"/>
    </view>
  </view>
</scroll-view>
