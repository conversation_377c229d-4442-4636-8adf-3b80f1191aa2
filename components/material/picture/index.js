const App = getApp();

Component({
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },
  properties: {
    list: {
      type: Array,
      value: [],
      observer: function(nv, ov){
        var list = nv.map(v => {
          if(!v.materialId){
            v.materialId = v.id
          }
          return v
        })
        this.setData({
          newList: list
        })
      }
    },
    source: String,
    activeBar: {
      type: Object,
      observer: function(nv){
        this.setData({
          selected: [],
          selectItem: []
        })
      }
    },
    materialSource: Number,
    clearSelected: {
      type: <PERSON><PERSON><PERSON>,
      observer: function(v){
        if(v === true){
          this.setData({
            selected: [],
            selectItem: []
          })
        }
      }
    },
    selectedList: {
      value: Array,
      value: [],
      observer: function(nv, ov){
        if (JSON.stringify(nv) === JSON.stringify(ov)) {return}
        const selectedIds = nv.map(item => item.materialId)
        this.setData({
          selected: selectedIds,
          selectItem: nv
        })
      }
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    selected: [],
    selectItem: [],
    newList: []
  },
  lifetimes: {
    attached: function () {
      this.setData({
        entry: App.globalData.entry,
      })
    }
  },
  observers: {
    "selected": function (val) {
      this.triggerEvent('select', {
        selected: val,
        selectItem: this.data.selectItem,
        materialSource: this.data.materialSource,
        msgtype: this.data.selectItem[0] && this.data.selectItem[0].type == 1 ? 'image' : 'video',
        msgtypeNum: 1
      })
    }
  },

  methods: {
    handleCheckbox(e) {
      let id = e.detail.materialId
      let index = this.data.selected.indexOf(id)
      if (index != -1) {
        this.data.selected.splice(index, 1)
        this.data.selectItem.splice(index, 1)
      } else {
        if(this.data.selected.length == 9 && this.data.entry == 'normal'){
          App.toast('最多只能选择9张图片')
        }else{
          this.data.selected.push(id)
          this.data.selectItem.push(e.detail)
        }
      }
      this.setData({
        selected: this.data.selected,
        selectItem: this.data.selectItem
      })
      this.triggerEvent('changeSelect', this.data.selectItem )
    },
    handleCollection(e) {
      let index = this.data.newList.findIndex(v => v.id == e.detail.id)
      this.data.newList.splice(index, 1)
      this.setData({
        newList: this.data.newList
      })

      if(!this.data.newList.length){
        this.triggerEvent('refresh')
      }
    },
    handleLinkPreview() {
      if(this.data.selected.length == 1) {
      }else{
        wx.navigateTo({
          url: `/pages/preview/preview?ids=${this.data.selected.toString()}`,
        })
      }
      
    },
    scrolltolower(e){
      this.triggerEvent('loadmore')
    },
    handleComplete(){
      this.setData({
        selected: [],
        selectItem: []
      })
    },
   updatePreviewShow(e){
      this.triggerEvent("updatePreviewShow",e.detail);
    },
    handlePreview(e){
      let _urls = this.data.newList.concat()
      App.globalData.is_preview = true
      wx.previewImage({
        current: e.detail.productUrl,
        urls: _urls.map(v => {
          return v.productUrl
        })
      })
    }
  }

})