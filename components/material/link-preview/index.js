Component({
    options: {
        addGlobalClass: true, 
        multipleSlots: true
    },
    properties: {
        showPreview: <PERSON>olean,
        previewData: {
            type: Object,
            value: {}
        }
    },
    methods: {
        setClipboard() {
            wx.setClipboardData({
              data: this.data.previewData.link,
              success: function (res) {
                wx.showToast({
                  title: '复制成功',
                  icon: 'none'
                });
              },
              fail: function (res) {
                wx.showToast({
                  title: '复制失败',
                  icon: 'none'
                });
              }
            })
          },
          closePreview() {
            this.triggerEvent('close')
          },
    }
});
