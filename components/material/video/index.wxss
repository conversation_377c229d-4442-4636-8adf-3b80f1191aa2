/* components/material/video/index.wxss */
@import '../../../app.wxss';
.video-page-wrapper {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  margin: 0 auto;
  width: 100vw;
  height: 100%;
  overflow-y: scroll;
  padding: 0 10rpx;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  background: #fff;
}
.video-item-box{
  width: 50%;
}
.video-item-box-left{
  margin-right: 5rpx;
}
.video-item-box-right{
  margin-left: 5rpx;
}