// components/material/video/index.js
import {
  getArrImgHeight
} from "../../../utils/waterfall";
const App = getApp();

Component({
  options: {
    addGlobalClass: true, 
    multipleSlots: true
  },
  properties: {
    list: {
      type: Array,
      value: [],
      observer:  function (nv, ov) {
        var list = nv.map(v => {
          if(!v.materialId){
            v.materialId = v.id
          }
          return v
        })
        this.setData({
          originList: list
        })
        this.getNewList()
      }
    },
    source: String,
    activeBar: {
      type: Object,
      observer: function(nv){
        this.setData({
          selected: [],
          selectItem: []
        })
      }
    },
    materialSource: Number,
    clearSelected: {
      type: <PERSON><PERSON><PERSON>,
      observer: function(v){
        if(v === true){
          this.setData({
            selected: [],
            selectItem: []
          })
        }
      }
    },
    selectedList: {
      value: Array,
      value: [],
      observer: function(nv, ov){
        if (JSON.stringify(nv) === JSON.stringify(ov)) {return}
        const selectedIds = nv.map(item => item.id)
        this.setData({
          selected: selectedIds,
          selectItem: nv
        })
      }
    },
  },
  observers: {
    "selected": function (val) {
      this.triggerEvent('select', {
        selected: val,
        selectItem: this.data.selectItem,
        msgtype: 'video',
        msgtypeNum: 2
      })
    }
  },

  data: {
    newList: [],
    selected: [],
    selectItem: [],
    playId: null,
    originList: []
  },
  lifetimes: {
    attached: function () {
      this.setData({
        entry: App.globalData.entry,
      })
    }
  },

  methods: {
    handleComplete(){
      this.setData({
        selected: [],
        selectItem: []
      })
    },
    getNewList: async function(){
      App.showLoading();
      var myArr = await getArrImgHeight(this.data.originList, 'videoPictureLink')
      this.setData({
        newList: myArr
      })
      App.hideLoading();
    },
    playVideo(e) {
      let id = e.detail.id
      this.setData({
        playId: id
      })
    },
    handleCheckbox(e) {
      let id = e.detail.id
      if (id == this.data.selected) {
        this.setData({
          selected: [],
          selectItem: []
        })
      } else {
        this.setData({
          selected: [id],
          selectItem: [e.detail]
        })
      }
    },
    handleCollection(e) {
      let index = this.data.originList.findIndex(v => v.id == e.detail.id)
      this.data.originList.splice(index, 1)
      this.setData({
        originList: this.data.originList
      })
      if(!this.data.originList.length){
        this.triggerEvent('refresh')
      }else{
        this.getNewList()
      }
    },
    scrolltolower(e) {
      this.triggerEvent('loadmore')
    }
  },

})