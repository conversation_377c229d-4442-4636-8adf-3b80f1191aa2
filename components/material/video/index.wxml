<!--components/material/video/index.wxml-->
<scroll-view class="video-page-wrapper" scroll-y="{{true}}" lower-threshold="{{200}}" enable-flex="{{true}}"  bindscrolltolower="scrolltolower">
  <view class="video-item-box video-item-box-left">
    <videoItem data="{{item}}" wx:for="{{newList[0]}}" wx:key="index" bind:play="playVideo" selected="{{selected}}" playId="{{playId}}" source="{{source}}" bind:checkbox="handleCheckbox" bind:collection="handleCollection"  isSys="{{materialSource === 0}}" />
  </view>
  <view class="video-item-box video-item-box-right">
    <videoItem data="{{item}}" wx:for="{{newList[1]}}" wx:key="index" bind:checkbox="handleCheckbox" selected="{{selected}}" playId="{{playId}}" source="{{source}}" bind:play="playVideo" bind:collection="handleCollection"  isSys="{{materialSource === 0}}" />
  </view>
</scroll-view>

<!-- <shareBar wx:if="{{selected}}" selected="{{selected}}" selectItem="{{selectItem}}" msgtype="video" bind:complete="handleComplete"/> -->
