<!--components/material/picture-item/index.wxml-->
<wxs src="./index.wxs" module="tools" /> 
<view class="picture_item">
  <van-image use-loading-slot  class="picture_item_image" src="{{data.productUrl || '/assets/imgs/default-pic.jpg'}}" fit="contain"  lazy-load>
    <van-loading wx:if="{{data.productUrl}}" slot="loading" type="spinner" size="20" vertical />
  </van-image>
  <!-- <image class="picture_item_image" src="{{data.url}}" mode="aspectFill"></image> -->
  <view class="picture_item_content  {{tools.fn(selected, data.materialId) > 0 ? 'picture_item_content_active' : ''}}" bindtap="preview">
      <view class="picture_item_content_top flex align-center justify-between">
        <view class="picture_item_t_left" catchtap="handleCheckbox">
          <image src="../../../assets/imgs/checkbox.png" mode="widthFix" wx:if="{{tools.fn(selected, data.materialId) <= 0}}"></image>
          <view wx:else class="picture_item_t_left_active">
            {{tools.fn(selected, data.materialId)}}
          </view>
        </view>
        <view class="picture_item_t_center" >
          <view class="picture_item_t_new" wx:if="{{data.isNew}}">new</view>
        </view>  
        <view class="picture_item_t_right" catchtap="handleCollection" data-id="{{data.id}}" data-hasCollect="{{collection}}">
          <image src="../../../assets/imgs/{{collection ? 'collection_active.png' : 'collection.png'}}" mode="widthFix"></image>
        </view>  
      </view>
      <view class="picture_item_content_amount" wx:if="{{materialSource === 1 && data.amount}}">
        ¥ {{tools.formatPrice(data.amount)}}{{data.batchManagement === 'Yes'?'起':''}}
      </view>
      <!-- <view class="picture_item_content_tags" wx:if="{{data.tags && data.tags.length}}">
        <tag class="picture_item_content_tags_item" wx:for="{{data.tags}}" value="{{item}}"/>
      </view> -->
  </view>
</view>
