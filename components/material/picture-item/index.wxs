function fn(arr, arg) {
  var result = arr.indexOf(arg);
  return result + 1;
}

function formatPrice(num, round) {
  if (num == undefined || num == null || num == '') {
    return '0'
  }
  if (round) {
    num = Math.round(num)
  }
  var parts = num.toString().split(".");
  var reg = getRegExp('\B(?=(\d{3})+(?!\d))', 'g')
  parts[0] = parts[0].replace(reg,',');
  return parts.join(".");
}

module.exports = {
  fn:fn,
  formatPrice:formatPrice
};