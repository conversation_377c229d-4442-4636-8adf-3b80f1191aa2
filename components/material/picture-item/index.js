const App = getApp()
import {disCollectProduct,collectProduct, disCollectExtProduct, collectExtProduct } from '../../../api/contentlibrary'
Component({
  options: {
    multipleSlots: true 
  },
  properties: {
    data: {
      type: Object,
      value: {},
      observer(nv, ov, path) {
        if (nv) {
          this.setData({
            collection: nv.hasCollect || nv.hasCollect ? true: false
          })
        }
      }
    },
    selected: {
      type: Array
    },
    source: String,
    isSys: Boolean,
    materialSource: Number
  },
  /**
   * 组件的初始数据
   */
  data: {
    collection: false
  },
  lifetimes: {
    attached: function () {
      
    }
  },

  methods: {
    async handleCollection(event){
			const { id, hascollect } = event.currentTarget.dataset;
      const params = {
        productId: id
      }
      let result;
      if(this.data.isSys){
        result = hascollect ? await disCollectProduct(params) : await collectProduct(params)
      }else{
        result = hascollect ? await disCollectExtProduct(params) : await collectExtProduct(params)
      }
			if (result.code == 200) {
        this.setData({
          collection: hascollect ? false : true
        })
        if(this.data.source == 'collection' && !this.data.collection){
          App.toast('已取消收藏')
        }
      }else{
        App.toast('收藏失败，请重试', 'error')
      }
    },
    handleCheckbox(){
      this.triggerEvent('checkbox', this.data.data)
    },
    preview(){
      if (this.data.materialSource == 1) {
        this.triggerEvent("updatePreviewShow", { show: true, id: this.data.data.id?.toString() })
      }else{
        this.triggerEvent('preview', this.data.data)
      }
    },
  }
})