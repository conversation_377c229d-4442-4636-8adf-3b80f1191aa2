/* components/material/picture-item/index.wxss */
@import '../../../app.wxss';

.picture_item_image {
  width: 100%;
  height: 460rpx;
  vertical-align: bottom;
  background: #f6f5f3;
  display: block;
}
.picture_item_image image{
  border-radius: 2rpx;
}

.picture_item {
  position: relative;
}

.picture_item_content {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  padding: 20rpx;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.picture_item_content_amount{
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 30rpx;
  height: 50rpx;
  line-height: 50rpx;
  background: rgba(246, 245, 243, 0.5);
  backdrop-filter: blur(10px);
  padding: 0 21rpx;
  font-family: AvenirNext, AvenirNext;
  font-weight: 400;
  font-size: 26rpx;
  color: #0A1532;
  text-align: center;
  font-style: normal;
  z-index: 1;
  white-space: nowrap;
}

.picture_item_content_top image {
  width: 40rpx;
  height: 40rpx;
  vertical-align: bottom;
}

.picture_item_content_tags {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}

.picture_item_content_tags_item {
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.picture_item_t_left_active {
  background: rgba(219, 138, 6, 1);
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  line-height: normal;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: #fff;
  font-size: 13px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
          font-family: PingFangSC, PingFang SC;
}
.picture_item_content_active{
  background: rgba(0,0,0,0.5);
}
.picture_item_t_center{
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.picture_item_t_new{
  width: 60rpx;
  height: 30rpx;
  background: rgba(10,21,50,0.7);
  backdrop-filter: blur(10px);
  border-radius: 3rpx;
  text-align: center;
  line-height: 30rpx;
  font-weight: 400;
  font-size: 22rpx;
  color: #FFFFFF;
  margin: 0 30rpx;
  flex-shrink: 0;
}
.picture_item_t_left, .picture_item_t_right{
  padding: 20rpx;
  margin: -20rpx;
}