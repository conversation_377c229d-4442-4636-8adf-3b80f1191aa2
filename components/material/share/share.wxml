<view class="share_contain flex align-center justify-between" wx:if="{{selected.length}}" style="bottom: {{tabBarHeight}}rpx" >
    <view class="share_contain_left" wx:if="{{msgtype != 'news'}}"  catchtap="downloadImgsOrVideos">
      <image wx:if="{{!loading}}" src="../../../assets/imgs/download.png" mode="widthFix"></image>
      <image wx:else src="../../../assets/imgs/loading.gif" mode="widthFix"></image>
      <text>{{ !loading ? '保存至本地' : '保存中...'}}</text>
    </view>
      
    <view class="share_contain_center">
      已选<text>{{selected.length}}</text>件素材
    </view>
    <view class="share_contain_right flex align-center" bindtap="handleLinkPreview">
      立即分享
      <image src="../../../assets/imgs/arrow-right.png" mode="widthFix"></image>
    </view>
  </view>

  <van-popup show="{{ showPopup }}" position="bottom" z-index="99999999" close-on-click-overlay="{{true}}" bind:close="onCancel" >
    <view class="share_contain_normal">
        <view class="share_normal_top">立即分享</view>  
        <view class="share_normal_tip">
          <image class="" src="../../../assets/imgs/icon-tip.png" mode="widthFix"/>
          同一个客人一天仅限群发一次；同一个客人一天仅限发送朋友圈三次
        </view>  
        <view class="share_normal_types">
          <block wx:for="{{normalShareTypes}}">
            <view class="share_normal_types_cell {{(item.type == 'share' && msgtype == 'news') && 'share_normal_types_cell_disabled'}}" bindtap="onShare" data-item="{{item}}">
              <image src="{{item.icon}}" mode="widthFix"/>
              <text>{{item.name}}</text>
            </view>
          </block>
        </view>
        <view class="share_normal_cancel" bindtap="onCancel">取消</view>
    </view>
  </van-popup>
  
  