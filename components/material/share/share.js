const App = getApp()
import {
	writePhotosAlbum
} from '../../../utils/writePhotosAlbum.js'
import {
	saveExtShareRecord,
	saveShareRecord,
  uploadProductFile,
  uploadExtProductFile
} from '../../../api/contentlibrary'
Component({
	options: {
		addGlobalClass: true,
		multipleSlots: true,
	},

	properties: {
		selected: Array,
		msgtype: String,
		msgtypeNum: Number,
		selectItem: {
			type: Array,
			value: [],
		},
		source: Number,
	},
	data: {
		mediaId: "",
		shareList: [],
		errorIndex: [],
		loading: false,
		normalShareTypes: [
			{
				type: "share",
				icon: "../../../assets/imgs/icon-share.png",
				name: "转发",
			},
			{
				type: "group",
				icon: "../../../assets/imgs/icon-group.png",
				name: "群发",
			},
			{
				type: "moment",
				icon: "../../../assets/imgs/icon-moment.png",
				name: "朋友圈",
			},
		],
		showPopup: false,
		tabBarHeight: App.globalData.tabBarHeight
	},
	lifetimes: {
		attached: function () {
			this.setData({
				entry: App.globalData.entry,
			});
		},
	},
	pageLifetimes: {
		show: function () {
			if (!App.globalData.is_preview) {
				this.triggerEvent("complete");
			}
		},
	},
	methods: {
		downloadImgsOrVideos() {
			const { msgtype, loading } = this.data;
			if (loading) return;
      const _selected = this.data.selectItem.concat();
			const urls = _selected.map((v) => {
				return {
          url: v.productUrl,
          type: v.type
        };
			});
			this.downloadMultiple(urls);
		},
		downloadMultiple(urls) {
			var that = this;
      that.setData({
        loading: true,
        errorIndex: []
      });
      that
        .queue(urls)
        .then((res) => {
          that.setData({
            loading: false,
          });
          let msg = "下载成功";
          if (that.data.errorIndex.length) {
            msg += ",第" + that.data.errorIndex.toString() + "张下载失败！";
            App.toast(msg);
          } else {
            App.toast(msg, "none");
          }

          that.triggerEvent("complete");
        })
        .catch((err) => {
          that.setData({
            loading: false,
          });
        });
		},
		queue(urls) {
			var that = this;
			let promise = Promise.resolve();
			urls.forEach((item, index) => {
				promise = promise.then(() => {
					return that.download(item, index + 1);
				});
			});
			return promise;
		},
		download(item, index) {
			var that = this;
			return new Promise((resolve, reject) => {
				wx.downloadFile({
					url: item.url,
					success: function (res) {
            var temp = res.tempFilePath;
            const fun = item.type === 2 ? wx.saveVideoToPhotosAlbum : wx.saveImageToPhotosAlbum
						fun({
							filePath: temp,
							success: function (res) {
								resolve(res);
							},
							fail: function (err) {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                })
								that.data.errorIndex.push(index);
								reject(res);
							},
            });
            
					},
					fail: function (err) {
            that.data.errorIndex.push(index);
            App.toast("网络不佳，请稍后再试");
						reject(err);
					},
				});
			});
		},
		onCancel() {
			this.setData({
				showPopup: false,
			});
		},
		async handleLinkPreview() {
			const that = this;
			const { selected, source } = this.data;
			if (this.data.entry == "normal") {
				this.setData(
					{
						showPopup: true,
					},
					() => {
					}
				);
			} else { // 1v1
				if (source == 1) { 
          if (this.data.msgtype === 'image') {
            this.linkContentPreview(3, () => {
              that.triggerEvent("complete");
            })
          } else if (this.data.msgtype === 'video') {
            const result = await uploadExtProductFile({
              productIds: selected,
              productType: 2
            });
            if (result.code == 200 && result.data) {
              let mediaId = result.data[0].fileMediaList[0].mediaId;
              that.setData({
                mediaId: mediaId,
              });
              // test
              this.checkSession();
            } else {
              wx.showModal({
                title: "提示",
                content: "分享失败，请退出重试。" + JSON.stringify(result),
                showCancel: false,
              });
            }
          } else {
            this.checkSession();
          }
				} else { 
					if (selected.length == 1) {
						App.showLoading();
						if (this.data.msgtype == "news") {
							this.checkSession();
						} else {
							const result = await uploadProductFile({
								productIds: selected,
              });
							if (result.code == 200 && result.data) {
								let mediaId = result.data[0].mediaId;
								that.setData({
									mediaId: mediaId,
								});
								// test
								this.checkSession();
							} else {
								wx.showModal({
									title: "提示",
									content: "分享失败，请退出重试。" + JSON.stringify(result),
									showCancel: false,
								});
							}
						}
					} else {
						this.linkPreview(3, () => {
							that.triggerEvent("complete")
						})
					}
				}
			}
		},
		async onShare(e) {
			const {source, msgtype} = this.data
      const data = e.currentTarget.dataset.item;
			if (source != 1 && data.type != "share" && msgtype != "news" || (source == 1 && msgtype == 'video' && data.type != "share")) {
        App.showLoading();
        let result = null
        if (source == 1) {
          result = await uploadExtProductFile({productIds: this.data.selected, productType: msgtype == 'video' ? 2 : msgtype == 'image' ? 1 : 3 }) 
        } else {
          result = await uploadProductFile({
            productIds: this.data.selected,
          });
        }
				if (result.code == 200 && result.data) {
					this.setData({
						shareList: this.data.selectItem.map((v) => {
              let mediaId = ''
              if (source == 1) {
                mediaId = result.data.filter((item) => item.productId == v.id)[0].fileMediaList[0].mediaId
              } else {
                mediaId = result.data.filter((item) => item.productId == v.id)[0].mediaId
              }
							return {
								...v,
								mediaId
							};
						}),
					});
				} else {
          App.hideLoading();
        }
			} else {
				this.setData({
					shareList: this.data.selectItem,
				});
			}
			if(source == 1){
				switch (data.type) {
					case "share":
						if(msgtype == 'image'){
							this.linkContentPreview(4)
						}else if(msgtype == 'video'){
							this.linkPreview(4)
						}
						break;
					case "moment":
						this.shareToExternalMoments();
						this.shareRecord(data.type);
						break;
					case "group":
						if(msgtype == 'image'){
							this.linkContentPreview(1)
						}else {
							this.shareToExternalContact();
							this.shareRecord(data.type);
						}
						break;
				}
			}else{
				switch (data.type) {
					case "share":
						this.linkPreview(4)
						break;
					case "moment":
						this.shareToExternalMoments();
						this.shareRecord(data.type);
						break;
					case "group":
						this.shareToExternalContact();
						this.shareRecord(data.type);
						break;
				}
			}
			this.onCancel();
			this.triggerEvent("complete");
		},
		getShareParams() {
			const attachments = [];
			this.data.shareList.forEach((v) => {
				switch (v.type) {
					case 1:
						attachments.push({
							msgtype: "image",
							image: {
								imgUrl: v.productUrl,
							},
						});
						break;
					case 2:
						attachments.push({
							msgtype: "video",
							video: {
								mediaid: v.mediaId,
							},
						});
						break;
					case 3:
						attachments.push({
							msgtype: "link",
							link: {
								title: v.title,
								imgUrl: v.productUrl,
								desc: v.content,
								url: v.link,
							},
						});
						break;
				}
			});
			return attachments;
		},
		shareToExternalContact() {
			const that = this;
			wx.qy.shareToExternalContact({
				text: {
					content: "",
				},
				attachments: this.getShareParams(),
				success: (res) => {
					App.hideLoading();
					if (res.errMsg == "qy__shareToExternalContact:ok") {
					} else {
						wx.showModal({
							title: "提示",
							content: "分享群发失败，请退出重试。" + JSON.stringify(res),
							showCancel: false,
						});
					}
					that.triggerEvent("complete");
				},
			});
		},
		shareToExternalMoments() {
			const that = this;
      let content = this.data.shareList.length === 1?this.data.shareList[0]?.content : '';
			wx.qy.shareToExternalMoments(
				{
					text: {
						content: content,
					},
					attachments: this.getShareParams(),
				},
				function (res) {
					App.hideLoading();
					if (res.err_msg == "shareToExternalMoments:ok") {
					} else {
						wx.showModal({
							title: "提示",
							content: "分享朋友圈失败，请退出重试。" + JSON.stringify(res),
							showCancel: false,
						});
					}
					that.triggerEvent("complete");
				}
			);
		},
		sendChatMessage() {
			const that = this;

			let content = {};
			switch (this.data.msgtype) {
				case "image":
					content = {
						image: {
							mediaid: this.data.mediaId,
						},
					};
					break;
				case "video":
					content = {
						video: {
							mediaid: this.data.mediaId,
						},
					};
					break;
				case "news":
					let _data = this.data.selectItem[0];
					content = {
						news: {
							link: _data.link, 
							title: _data.title,
							desc: _data.content,
							imgUrl: _data.productUrl,
						},
					};
					break;
			}
      App.showLoading();
      that.shareRecord();
			wx.qy.sendChatMessage({
				msgtype: this.data.msgtype,
				enterChat: true,
				...content,
				success: function (res) {
					App.toast("分享成功");
					that.triggerEvent("complete");
				},
				fail: function (res) {
					App.hideLoading();
					if (res.errMsg != "qy__sendChatMessage:fail cancel") {
						wx.showModal({
							title: "提示",
							content: "分享失败，请退出重试。" + JSON.stringify(res),
							showCancel: false,
						});
					}
				},
			});
		},
		async shareRecord(type) {
			let shareType = ''
			if (type == "moment") {
				shareType = 2
			} else if (type == "group") {
				shareType = 1
			} else if (type == "share"){
				shareType = 4
			}else {// 1V1
				shareType = 3
			}
			const params = {
				productIds: this.data.selected,
				productType: this.data.msgtypeNum,
				shareType: shareType,
				externalUserIds: shareType == 3 ? [App.globalData.externalUserId] : []
			}
			const result = this.data.source == 1 ? await saveExtShareRecord(params) : await saveShareRecord(params)
			if (result.code == 200) {
			}
		},
		linkPreview(shareType, fn){
      const {selected,msgtypeNum,source } = this.data
      App.hideLoading()
			wx.navigateTo({
				url: `/pages/preview/preview?ids=${selected.toString()}&productType=${msgtypeNum}&shareType=${shareType}&materialType=${source}`,
				complete: function(){
					fn && fn()
				}
			});
		},
		linkContentPreview(shareType, fn){
      App.hideLoading()
			wx.navigateTo({
				url: `/pages/contentSharing/contentSharing?ids=${this.data.selected.toString()}&productType=${this.data.msgtypeNum}&shareType=${shareType}`,
				complete: function () {
					fn && fn()
				},
			});
		},
		checkSession() {
			const that = this;
			wx.qy.checkSession({
				success() {
					that.sendChatMessage();
				},
				fail() {
					wx.qy.login({
						success(res) {
							that.sendChatMessage();
						},
						fail(err) {
						},
					});
				},
			});
		},
	},
});