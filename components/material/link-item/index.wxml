<!--components/material/link-item/index.wxml-->
<view class="link_item flex">
  <view class="link_item_left" bindtap="handleCheckbox">
    <image mode="widthFix" src="../../../assets/imgs/{{selected == data.id ? 'checkbox_active.png' : 'checkbox.png'}}"></image>
  </view>
  <view class="link_item_right flex1">
    <view class="link_item_top">
      <view class="link_item_top_left flex1">
        <view class="link_item_top_left_title ellipsis">
          <view class="picture_item_t_new" wx:if="{{data.isNew}}">new</view>
          <view>{{data.title}}</view>
        </view>
        <view class="link_item_top_left_content ellipsis3">{{data.content}}</view>
      </view>
      <view class="link_item_top_right">
        <image class="link_item_top_right_image" src="{{data.productUrl}}" mode="aspectFill"></image>
        <view class="link_item_top_right_collection" bindtap="handleCollection" data-id="{{data.id}}" data-hasCollect="{{collection}}">
          <image mode="widthFix" src="../../../assets/imgs/{{collection ? 'collection_active.png' : 'collection.png'}}"></image>
        </view>
      </view>
    </view>
    <!-- <view class="link_item_tags flex align-center" wx:if="{{data.tags && data.tags.length}}">
      <view class="link_item_tags_item" wx:for="{{data.tags}}">{{item.tagName}}</view>
    </view> -->
    <view class="link_item_bottom flex align-center justify-between" bindtap="previewContent">
      <view class="link_item_bottom_left flex align-center">
        <image src="../../../assets/imgs/link.png" mode="widthFix"></image>
        <text wx:if="{{data.source}}">{{data.source}}</text>
      </view>
      <view class="link_item_bottom_right flex align-center">
        <text>预览内容</text>
        <image src="../../../assets/imgs/right.png" mode="widthFix"></image>
      </view>
    </view>
  </view>
</view>

<!-- 防止页面滚动穿透 -->
<page-meta page-style="{{ showPreview ? 'overflow: hidden;' : '' }}" />
<van-popup show="{{ showPreview }}" position="bottom" z-index="{{99999999}}" close-on-click-overlay="{{false}}">
  <cover-view class="preview_popup" wx:if="{{showPreview}}">
    <cover-view class="preview_popup_tip">预览内容提醒</cover-view>
    <cover-view class="preview_popup_title">请复制以下链接在浏览器打开</cover-view>
    <cover-view class="preview_popup_link">{{data.link}}</cover-view>
    <cover-view class="preview_popup_button" bindtap="setClipboard">
      <cover-image class="preview_popup_icon" mode="widthFix" src="../../../assets/imgs/link.png"></cover-image>
      <cover-view class="preview_popup_text">复制链接</cover-view>
    </cover-view>
    <cover-view class="divider"></cover-view>
    <cover-view class="preview_popup_cancel" bindtap="closePreview">取消</cover-view>
  </cover-view>
</van-popup>