/* components/material/link-item/index.wxss */
@import '../../../app.wxss';

.link_item {
  background: #fff;
  border-radius: 2rpx;
  margin-bottom: 10rpx;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  padding: 30rpx;
  padding-bottom: 0;
  width: 100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  margin-bottom: 10rpx;
}

.link_item_left image {
  width: 40rpx;
  height: auto;
  margin-right: 20rpx;
}

.link_item_top {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding-bottom: 20rpx;
}

.link_item_top_left {
  margin-right: 30rpx;
  width: 0;
}

.link_item_top_left_title {
  font-size: 14px;
  font-weight: bolder;
  color: rgba(0, 0, 0, 1);
  line-height: 36rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.picture_item_t_new{
  width: 60rpx;
  height: 30rpx;
  background: rgba(10,21,50,0.7);
  backdrop-filter: blur(10px);
  border-radius: 3rpx;
  text-align: center;
  line-height: 30rpx;
  font-weight: 400;
  font-size: 22rpx;
  color: #FFFFFF;
  flex-shrink: 0;
  margin-right: 10rpx;
}

.link_item_top_left_content {
  font-size: 12px;
  color: rgba(107, 107, 107, 1);
  line-height: 32rpx;
}

.link_item_top_right .link_item_top_right_image {
  width: 150rpx;
  height: 150rpx;
  vertical-align: bottom;
}

.link_item_top_right {
  position: relative;
}

.link_item_top_right_collection {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
}

.link_item_top_right_collection image {
  width: 40rpx;
  height: auto;
}

.link_item_bottom {
  padding: 23rpx 0;
  border-top: 1px dashed rgba(217, 217, 217, 1);
}

.link_item_bottom image {
  height: auto;
}

.link_item_bottom_left image {
  width: 24rpx;
  margin-right: 10rpx;
}

.link_item_bottom_left text {
  font-size: 10px;
  color: rgba(107, 107, 107, 1);
}

.link_item_bottom_right image {
  width: 9rpx;
  margin-left: 10rpx;
}

.link_item_bottom_right text {
  font-size: 10px;
  color: rgba(0, 0, 0, 1);
}

/* 预览弹窗 */
.preview_popup {
  background: #fff;
  text-align: center;
}

.preview_popup_tip {
  background: rgba(246, 244, 239, 1);
  color: rgba(0, 0, 0, 1);
  font-size: 13px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 34rpx 0;
  line-height: 32rpx;
}

.preview_popup_title {
  color: rgba(0, 0, 0, 1);
  font-size: 15px;
  font-weight: bolder;
  padding: 60rpx 0 40rpx;
}

.preview_popup_link {
  font-size: 13px;
  color: rgba(107, 107, 107, 1);
  line-height: 40rpx;
  padding: 0 100rpx;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  word-break: break-all;
}

.preview_popup_button {
  margin: 60rpx auto;
  width: 300rpx;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: rgba(0, 0, 0, 1);
  font-size: 13px;
  border: 1px solid rgba(0, 0, 0, 1);
  padding: 30rpx 0;
  position: relative;
}

.preview_popup_button .preview_popup_icon {
  width: 30rpx;
  height: auto;
  margin-right: 10rpx;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
  left: 75rpx;
}

.preview_popup_text {
  padding-left: 40rpx;
}

.preview_popup_cancel {
  height: 90rpx;
  padding-top: 33rpx;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
}

.divider {
  width: 100%;
  height: 2rpx;
  background-color: rgba(0, 0, 0, 0.1);
}
.link_item_tags{
  padding-bottom: 10rpx;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.link_item_tags_item {
  padding: 0 15rpx;
  height: 42rpx;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  line-height: normal;
  border-radius: 21rpx;
  border: 2rpx solid rgba(217, 217, 217, 1);
  color: rgba(107, 107, 107, 1);
  font-size: 10px;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}