// components/material/link-item/index.js
const App = getApp()
import {disCollectProduct,collectProduct, disCollectExtProduct, collectExtProduct } from '../../../api/contentlibrary'
Component({
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },
  properties: {
    data: {
      type: Object,
      value: '',
      observer: function (nv, ov) {
        if (nv) {
          this.setData({
            collection: nv.hasCollect || nv.hasCollect ? true: false
          })
        }
      }
    },
    selected: {
      type: String,
      value: -1
    },
    source: String,
    isSys: Boolean
  },

  /**
   * 组件的初始数据
   */
  data: {
    showPreview: false,
    collection: false
  },
  lifetimes: {
    attached: function () {
      this.setData({
        entry: App.globalData.entry
      })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    previewContent() {
      this.triggerEvent('preview', this.data.data)
    },
    closePreview() {
      this.setData({
        showPreview: false
      })
    },
    setClipboard() {
      wx.setClipboardData({
        data: this.data.data.link,
        success: function (res) {
          wx.showToast({
            title: '复制成功',
          });
        },
        fail: function (res) {
          wx.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      })
    },
    async handleCollection(event){
			const { id, hascollect } = event.currentTarget.dataset;
      const params = {
        productId: id
      }
      let result;
      if(this.data.isSys){
        result = hascollect ? await disCollectProduct(params) : await collectProduct(params)
      }else{
        result = hascollect ? await disCollectExtProduct(params) : await collectExtProduct(params)
      }
      if (result.code == 200) {
        this.setData({
          collection: hascollect ? false : true
        })
        if(this.data.source == 'collection' && !this.data.collection){
          App.toast('已取消收藏')
        }
      }else{
        App.toast('收藏失败，请重试', 'error')
      }
    },
    handleCheckbox() {
      this.triggerEvent('checkbox', this.data.data)
    }
  }
})