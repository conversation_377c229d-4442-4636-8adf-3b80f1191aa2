/* components/defaultPage/defaultPage.wxss */
.default_page{
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  text-align: center;
  position: absolute;
  left: 50%;
  top: 40%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.default_page_icon{
  width: 100rpx;
  height: auto;
  margin: 0 auto 60rpx;
}
.default_page_message{
  font-size: 15px;
  color: rgba(0, 0, 0, 0.5);
  /* font-weight: bolder; */
  margin-bottom: 80rpx;
}
.default_page_button{
  border: 2rpx solid rgba(0, 0, 0, 1);
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  padding: 0 75rpx;
  font-size: 13px;
  font-weight: bolder;
  min-width: 280rpx;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

.default_page_message_sub{
  font-size: 13px;
  color: rgba(107, 107, 107, 1);
  margin-top: -60rpx;
}