/* components/tree/index.wxss */
.tree_popup {
  width: 100vw;
  height: 100vh;
  background: #fff;
  -webkit-box-shadow: inset 0 -10rpx 10rpx rgba(0, 0, 0, 0.1);
          box-shadow: inset 0 -10rpx 10rpx rgba(0, 0, 0, 0.1);
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  overflow-y: scroll;
  padding: 20rpx;
  position: relative;
  padding-bottom: 276rpx;
}

.tree_popup_title {
  height: 100rpx;
  line-height: 100rpx;
  padding: 0 30rpx;
  font-size: 13px;
  color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  background: #F6F4EF;
}

.tree_popup_title image {
  width: 24rpx;
  height: 24rpx;
  padding: 10rpx;
  margin-right: -10rpx;
}

.tree_popup_ul {
  height: 106rpx;
  padding: 0 30rpx;
  font-size: 15px;
  color: #000000;
  background: #fff;
}
/* .tree_popup_ul:active{
  background: #F6F4EF;
} */
.tree_popup_ul_active{
  background: #F6F4EF;
  font-weight: bolder;
}

.tree_popup_ul image {
  width: 24rpx;
  height: auto;
  padding: 20rpx;
  margin-right: -20rpx;
}

.tree_popup_ul_item {
  padding: 17rpx 0rpx;
  background: #FAFAFA;
}

.tree_popup_ul_update {
  display: inline-block;
  width: 60rpx;
  height: 30rpx;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  line-height: normal;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 15rpx;
  color: #fff;
  background-color: #000;
  font-size: 10px;
  font-weight: bolder;
  margin-left: 15rpx;
}

.search_top {
  position: relative;
}
.search_top input {
  width: 100%;
  height: 70rpx;
  background: rgba(243, 246, 249, 1);
  border: none;
  padding: 0 60rpx 0 80rpx;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  font-size: 12px;
  color: #000;
  border-radius: 20rpx;
}

.search_top .icon-search {
  position: absolute;
  width: 25rpx;
  height: auto;
  left: 35rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
}
.search_top .icon-close {
  position: absolute;
  z-index: 999;
  width: 80rpx;
  height: 80rpx;
  right: 0rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
}
.icon-close image{
    width: 20rpx;
    height: auto;
    position:absolute;
    top:50%;
    left:50%;
    margin-top:-10rpx;
    margin-left:-10rpx;
}


.tree-footer{
  background-color: #fff;
  position: fixed;
  bottom: 140rpx;
  left: 0;
  width: 100%;
  padding: 16rpx 18rpx;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.tree-footer .btn{
  width: 346rpx;
  height: 62rpx;
  border-radius: 8rpx;
  border: 2rpx solid #BCBCBC;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  letter-spacing: 1px;
  color: #636676;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.tree-footer .btn.btn-confirm{
  background-color: rgba(219, 138, 6, 1);
  border-color: rgba(219, 138, 6, 1);
  color: #fff;
}

.null{
  padding: 140rpx;
  text-align: center;
  font-weight: 400;
  font-size: 30rpx;
  color: #473F36;
}

.tree-list{
  padding: 0 10rpx;
}
.tree-list-wrap{
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(233, 233, 235, 1);
}
.tree-list-item{
  padding: 20rpx 0;
  font-weight: 400;
  font-size: 26rpx;
  color: #473F36;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.item-name{
  display: flex;
  align-items: center;
}
.text{
  flex: 1;
}
.item-new{
  width: 60rpx;
  height: 30rpx;
  background: #DB8A06;
  border-radius: 3rpx;
  text-align: center;
  line-height: 30rpx;
  font-weight: 400;
  font-size: 22rpx;
  color: #FFFFFF;
  margin: 0 20rpx;
  flex-shrink: 0;
}
.tree-list-item .item-icon{
  width: 38rpx;
  height: 38rpx;
}