const App = getApp();

Component({
	options: {
		addGlobalClass: true, 
		multipleSlots: true,
	},

	properties: {
		show: Boolean,
    reset: {
			type: <PERSON><PERSON>an,
			value: false,
			observer(newVal, oldVal) {
			},
    },
		data: {
			type: Array,
			value: [],
			observer(newVal, oldVal) {
				this.setData({ filteredList: newVal });
			},
		},
		paddingtop: {
			type: Number,
			value: 122,
		},
	},
	data: {
		navHeight: App.globalData.navHeight,
		expandIds: [],
		selected: [],
		keyword: "",
		filteredList: [],
    filteredNull:false
	},
	lifetimes: {
		attached: async function () {},
	},
	ready: function (e) {},
	methods: {
		onReset() {
			this.setData({
				selected: [],
        keyword: "",
        filteredList: this.data.data,
        filteredNull:false,
			});
		},
		onInput(e) {
			const keyword = e.detail.value;
			this.setData({ keyword });
      this.filterList(keyword);
		},
		filterList(keyword) {
      const list = JSON.parse(JSON.stringify(this.data.data));
			const filteredList1 = list?.map((item) => {
				if (item.name.includes(keyword)) {
          item.accord = true;
					return item;
				}
				if (item.children && item.children.length > 0) {
					for (let child of item.children) {
						if (child.name.includes(keyword)) {
							return item;
						}
					}
				}
				return false;
			});
      const filteredList = filteredList1.map((item)=>{
        if(item.accord){
          return item
        }
        if(item.children && item.children.length>0){
          item.children = item.children.filter((child)=>{
            return child.name.includes(keyword)
          })
          return item
        }
      })
      let filteredNull = filteredList.every(item => item === undefined || !item);
			this.setData({ filteredList,filteredNull });
		},
		onClear() {
			this.setData({ keyword: "",filteredNull: false, filteredList: this.data.data });
		},
		onConfirm() {
			this.triggerEvent("confirm", this.data.selected);
		},
		closeTreePopup() {
			this.triggerEvent("close");
		},
		onTreeItemClick(e) {
      const { item } = e.currentTarget?.dataset;
      const { expandIds } = this.data;
      var index = expandIds.indexOf(item.id);
			if (index > -1) {
				expandIds.splice(index, 1);
			} else {
        expandIds.push(item.id);
      }
			this.setData({
				expandIds: expandIds,
			});
		},
		
		onCheckChange(e) {
      const item = e.detail;
      const index = this.data.selected.indexOf(item.id);
			if (index > -1) {
				this.data.selected.splice(index, 1);
			} else {
        this.data.selected.push(item.id);
      }
			
			this.setData({
				selected: this.data.selected,
			});
		},
	},
});
