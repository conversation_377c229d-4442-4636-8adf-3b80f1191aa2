/* components/tree/item.wxss */
.tree-item{
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
            justify-content: space-between;
    padding-left: 40rpx;
    font-weight: 400;
    font-size: 26rpx;
    color: #473F36;
    /* margin-bottom: 32rpx; */
}

.item-name{
  display: flex;
  align-items: center;
}
.text{
  flex: 1;
  padding-left: 40rpx;
}
.item-new{
  width: 60rpx;
  height: 30rpx;
  background: #DB8A06;
  border-radius: 3rpx;
  text-align: center;
  line-height: 30rpx;
  font-weight: 400;
  font-size: 22rpx;
  color: #FFFFFF;
  margin: 0 20rpx;
}

.tree-item image{
    width: 38rpx;
    height: 38rpx;
    flex-shrink: 0;
}