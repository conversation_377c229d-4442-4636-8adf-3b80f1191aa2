<!-- components/tree/index.wxml -->
<wxs src="./index.wxs" module="tools" />
<van-popup show="{{ show }}" position="left" bind:close="closeTreePopup" close-on-click-overlay="{{true}}">
  <view class="tree_popup" style="padding-top: {{navHeight + paddingtop}}px;">
    <view class="search_top">
      <image src="../../assets/imgs/search.png" mode="widthFix" class="icon-search"></image>
      <input placeholder-class="search_top_placeholder" value="{{keyword}}" type="text" placeholder="请输入要搜索的标签" bindconfirm="onInput" bindinput="onInput" />
      <view  wx:if="{{keyword}}" class="icon-close" bindtap="onClear" >
        <image  src="../../assets/imgs/icon-close.png" mode="widthFix" class="icon-close-image" ></image>
      </view>
    </view>
    <view class="tree-list">
      <view wx:if="{{filteredNull}}" class="null">没有符合条件的标签</view>
      <block wx:for="{{filteredList}}" wx:key="index">
        <view class="tree-list-wrap" wx:if="{{item.children}}">
          <view class="tree-list-item" bindtap="onTreeItemClick" data-item="{{item}}">
            <view class="item-name">
              <view class="text">{{item.name}}</view>
              <view class="item-new" wx:if="{{item.hasNew}}">new</view>
            </view>
            <image class="item-icon" src="../../assets/imgs/jia.png" mode="widthFix" wx:if="{{!tools.indexOf(expandIds, item.id)}}"  />
            <image class="item-icon" src="../../assets/imgs/jian.png" mode="widthFix" wx:else />
          </view>
          <view style="padding: 14rpx 0;" wx:if="{{tools.indexOf(expandIds, item.id)}}">
            <block wx:for="{{item.children}}" wx:for-item="itm" wx:for-index="idx" wx:key="idx" >
              <treeItem index="{{idx}}" arr="{{item.children}}" item="{{itm}}" actived="{{tools.indexOf(selected, itm.id)}}" bind:checkChange="onCheckChange"></treeItem>
            </block>
          </view>
        </view>
      </block>
    </view>
    <view class="tree-footer">
      <view class="btn btn-cancel" bindtap="onReset">重置</view>
      <view class="btn btn-confirm" bindtap="onConfirm">确认</view>
    </view>
  </view>
</van-popup>