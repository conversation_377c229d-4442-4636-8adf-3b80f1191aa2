// components/tree/item.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    item: {
      type: Object,
      value: {}
    },
    actived: {
      type: Boolean,
      value: false
    },
    index: {
      type: Number,
    },
    arr: {
      type: Array,
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    onCheckChange(){
      this.triggerEvent('checkChange', this.data.item)
    },
  }
})