/* components/navbar/index.wxss */

/*page { box-sizing: border-box; overflow: hidden; }*/
.mainPage {
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden;
    background: transparent;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
}

.mainPage>.mainPage_container {
    width: 100%;
    height: 100%;
    position: relative;
    -webkit-box-shadow: inset 0 -10rpx 10rpx rgba(0, 0, 0, 0.1);
            box-shadow: inset 0 -10rpx 10rpx rgba(0, 0, 0, 0.1);
}

.mainPage>.mainPage_container>.scroll-box {
    position: absolute;
    width: 100%;
    height: 100%;
}

.navbar_contain {
    width: 100%;
    position: absolute;
    left: 0;
}
.navbar_contain_title{
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
    font-size: 16px;
    color: #000000;
    font-weight: bolder;
    white-space: nowrap;
}
.navbar_contain_left{
    position: absolute;
    left: 29rpx;
    top: 50%;
    -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
            transform: translateY(-50%);
    width: 58rpx;
    height: 58rpx;
    border-radius: 50%;
    border: 1px solid rgba(0,0,0,0.08);
}
.navbar_contain_left image{
    width: 24rpx;
    height: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
}
.navbar_language {
    position: absolute;
    left: 29rpx;
    top: 50%;
    -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
            transform: translateY(-50%);
}

.navbar_language text {
    color: #6B6B6B;
    font-size: 12px;
}

.navbar_language text.active {
    color: #000000;
    font-size: 14px;
    font-weight: bolder;
}

.navbar_language text:nth-of-type(2) {
    color: #D9D9D9;
    font-size: 12px;
    margin: 0 7rpx;
}

.navbar_logo {
    position: absolute;
    width: 248rpx;
    height: auto;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
}

.navbar_wrap {
    position: fixed;
    z-index: 9999;
    width: 100%;
    background: #fff;
    left: 0;
    top: 0;
}