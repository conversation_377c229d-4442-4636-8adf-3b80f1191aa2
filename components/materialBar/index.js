const App = getApp()

Component({
    options: {
        addGlobalClass: true, 
        multipleSlots: true
    },
    properties: {
        activeType: Number
    },
    data: {
        types: [{
            name: '产品图册',
            type: 1
        }, {
            name: '其他图册',
            type: 0
        }],
    },
    lifetimes: {
        attached: function () {
        }
    },
    methods: {
        handleClickType(e) {
            let type = e.currentTarget.dataset.type
            this.triggerEvent('change', type);
        }
    }
});