.material_bar {
    padding: 0 60rpx;
    width: 100%;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    overflow-x: auto;
    -webkit-flex-wrap: nowrap;
        -ms-flex-wrap: nowrap;
            flex-wrap: nowrap;
    background: #fff;
    -webkit-box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(0,0,0,0.05);
            box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(0,0,0,0.05);
  }
  .material_bar::-webkit-scrollbar {
    display: none;
  }
  .material_bar_item{
    -webkit-box-flex: 0;
    -webkit-flex: none;
        -ms-flex: none;
            flex: none;
    padding: 34rpx 0 26rpx;
    margin-right: 70rpx;
    border-bottom: 2rpx solid #fff;
    font-family: PingFangSC, PingFang SC;
    font-weight: 300;
    font-size: 26rpx;
    color: #636676;
  }
  .material_bar_item view{
    position: relative;
  }
  .material_bar_item.home_bar_item_active{
    color: #0A1532;
    border-color: #0A1532;
  }
  .material_bar_item:last-child{
    margin-right: 0;
  }