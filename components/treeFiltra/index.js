const App = getApp();
import { queryAllFilterByCategoryId,queryProductCountByFilter } from '../../api/contentlibrary'

Component({
	options: {
		addGlobalClass: true,
		multipleSlots: true,
	},

	properties: {
		show: {
      type: <PERSON><PERSON><PERSON>,
      observer(newVal) {
        if(newVal) {
          // this.onTreeItemClick('init')
        }
      }
    },
    reset: {
			type: <PERSON><PERSON>an,
			value: false,
			observer(newVal, oldVal) {
			},
    },
		data: {
			type: Array,
			value: [],
			observer(newVal, oldVal) {
				this.setData({ filteredList: newVal });
			},
		},
		paddingtop: {
			type: Number,
			value: 122,
		},
	},
	data: {
		navHeight: App.globalData.navHeight,
		selected: [],
		keyword: "", 
		filteredList: [], 
    activeIndex: -1,
    categoryId:"",
    activeChildren:[],
    spreadList:[],
    filterNum:-1,
	},
	lifetimes: {
		attached: async function () {},
	},
	ready: function (e) {
    
  },
	methods: {
		onReset() {
			this.setData({
        keyword: "",
        filteredList: this.data.data,
        activeIndex: -1,
        activeChildren: [],
        spreadList:[],
        filterNum:-1,
        selected: [],
        categoryId: ''
			});
      this.onTreeItemClick('init')
		},
		onInput(e) {
			const keyword = e.detail.value; 
			this.setData({ keyword });
      this.onTreeItemClick('input');
		},
    onClear() {
			this.setData({ keyword: ""});
      this.onTreeItemClick('input');
		},
		filterList(keyword) {
      const list = JSON.parse(JSON.stringify(this.data.data));
			const filteredList1 = list?.map((item) => {
				if (item.name.includes(keyword)) {
          item.accord = true;
					return item;
				}
				if (item.children && item.children.length > 0) {
					for (let child of item.children) {
						if (child.name.includes(keyword)) {
							return item;
						}
					}
				}
				return false;
			});
      const filteredList = filteredList1.map((item)=>{
        if(item.accord){
          return item
        }
        if(item.children && item.children.length>0){
          item.children = item.children.filter((child)=>{
            return child.name.includes(keyword)
          })
          return item
        }
      })
			this.setData({ filteredList });
		},
		onConfirm() {
      let arr = this.data.selected.filter(subArr => subArr.length > 0);
			this.triggerEvent("confirm", {arr,id:this.data.categoryId});
		},
		closeTreePopup() {
			this.triggerEvent("close");
		},
		async onTreeItemClick(e) {
      let currentCategory = {};
      if(e === 'init' || e === 'input'){
        if (this.data.activeIndex === -1) {
          this.triggerEvent('categoryChange', "")
          return
        }
        currentCategory = this.data.data[this.data.activeIndex] 
      }else{
        const { item,index } = e.currentTarget.dataset;
        if(this.data.activeIndex === index){
          return
        }
        currentCategory = item;
        this.setData({
          activeIndex: index,
        });
      }
      
      this.setData({
        categoryId: currentCategory.id,
      });
      this.triggerEvent('categoryChange', currentCategory)
      let params = {
        categoryId: currentCategory.id,
        filterValue: this.data.keyword
      };
      const result = await queryAllFilterByCategoryId(params);
      if(result.code == 200 && result.data){
        let length = result.data.length;
        let nullList = []
        for (var i = 0; i < length; i++) {
          nullList.push([]);
        }
        if(e === 'input'){
          this.setData({
            activeChildren: result.data,
          });
        }else{
          this.setData({
            activeChildren: result.data,
            selected: nullList,
            filterNum: -1,
          });
        }
      }
		},
		
		async onCheckChange(e) {
      const { id,indexx } = e.currentTarget.dataset;
      let list = this.data.selected
      let indexxx = list[indexx].indexOf(id);
			if (indexxx > -1) {
				list[indexx].splice(indexxx, 1);
			} else {
        list[indexx].push(id);
      }
			this.setData({
				selected: list,
			});

      let arr = this.data.selected.filter(subArr => subArr.length > 0);
      if(arr.length == 0){
        this.setData({
          filterNum: -1,
        });
        return
      }
      let params = {
        filterIdsList: arr,
      };
      const result = await queryProductCountByFilter(params);
      if(result.code == 200){
        this.setData({
          filterNum: result.data,
        });
      }
		},
    spreadFun(e){
      const { id } = e.currentTarget.dataset;
      const { spreadList } = this.data;
      let index = spreadList.indexOf(id);
			if (index > -1) {
				spreadList.splice(index, 1);
			} else {
        spreadList.push(id);
      }
      this.setData({
				spreadList: spreadList,
			});
    },
	},
});
