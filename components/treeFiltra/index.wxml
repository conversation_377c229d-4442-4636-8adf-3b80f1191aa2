<!-- components/tree/index.wxml -->
<wxs src="./index.wxs" module="tools" />
<van-popup show="{{ show }}" position="left" bind:close="closeTreePopup" close-on-click-overlay="{{true}}">
  <view class="tree_popup" style="padding-top: {{navHeight + paddingtop}}px;">
    <view class="search_top">
      <image src="../../assets/imgs/search.png" mode="widthFix" class="icon-search"></image>
      <input placeholder-class="search_top_placeholder" value="{{keyword}}" type="text" placeholder="请输入要搜索的标签" bindconfirm="onInput" bindinput="onInput" />
      <view  wx:if="{{keyword}}" class="icon-close" bindtap="onClear" >
        <image  src="../../assets/imgs/icon-close.png" mode="widthFix" class="icon-close-image" ></image>
      </view>
    </view>
    <view class="tree-block">
        <view class="left">
          <view class="{{activeIndex == index ? 'item-wrap-active' : 'item-wrap'}}"  wx:for="{{filteredList}}" wx:key="index" bindtap="onTreeItemClick" data-item="{{item}}" data-index="{{index}}">
            <view class="item" >
              <view class="text">{{item.name}}</view>
            </view>
          </view>
        </view>
        <view class="right">
          <view class="item-content">
            <view class="li" wx:for="{{activeChildren}}" wx:for-item="itemm" wx:for-index="indexx" wx:key="indexx" >
              <view class="title">
                <text>{{itemm.thirdLevelFilterName}}</text>
                <view class="spread" wx:if="{{itemm.fourthLevelFilterList.length>6}}" bindtap="spreadFun" data-id="{{itemm.thirdLevelFilterKey}}">{{!tools.indexOf(spreadList, itemm.thirdLevelFilterKey)?"展开":"收起"}}<image src="../../assets/imgs/spread.png"/></view>
              </view>
              <view class="childrenBox">
                <block wx:for="{{itemm.fourthLevelFilterList}}" wx:for-item="itemmm" wx:for-index="indexxx" wx:key="indexxx">
                  <view wx:if="{{indexxx<6}}" class="{{tools.indexOf(selected[indexx], itemmm.filterId) ? 'tag-active' : 'tag'}}"
                  data-id="{{itemmm.filterId}}" data-indexx="{{indexx}}"
                  bindtap="onCheckChange">
                    {{itemmm.filterValue}}
                  </view>
                  <view wx:if="{{indexxx >=6 && tools.indexOf(spreadList, itemm.thirdLevelFilterKey)}}" class="{{tools.indexOf(selected[indexx], itemmm.filterId) ? 'tag-active': 'tag'}}"
                  data-id="{{itemmm.filterId}}" data-indexx="{{indexx}}"
                  bindtap="onCheckChange">
                    {{itemmm.filterValue}}
                  </view>
                </block>
                
              </view>
            </view>
            
          </view>
        </view>
    </view>
    <view class="tree-footer">
      <view class="btn btn-cancel" bindtap="onReset">重置</view>
      <view class="btn btn-confirm" bindtap="onConfirm">确认<block wx:if="{{filterNum>=0}}">({{filterNum}}产品)</block></view>
    </view>
  </view>
</van-popup>