Page({

  /**
   * 页面的初始数据
   */
  data: {
    setvalText:2,
    pageObj: ""
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(options);
    if(options.payFlag=='1'){
      this.setData({
        pageObj:options
      })
    }else{
      wx.navigateTo({
        url: `/remote-sales/pages/webview/webview?url=${options.backUrl || ''}`
      }) 
    }
  },
  onReady: function () {

  },
  goTopay(){
    let that =this
    wx.showLoading({
      title: '支付中',
    })
    wx.requestPayment({
        timeStamp: that.data.pageObj.timeStamp,
        nonceStr: that.data.pageObj.nonceStr,
        package: decodeURIComponent(that.data.pageObj.package),
        signType: that.data.pageObj.signType,
        paySign: decodeURIComponent(that.data.pageObj.paySign),
        success(res) {
          console.log("wx.requestPayment result:" + JSON.stringify(res)); 
          wx.hideLoading()
          that.getSetvelTime()   
        },
        fail(error) {
          wx.hideLoading()
          console.log(error.errMsg);
          if(error.errMsg&&error.errMsg.indexOf("requestPayment:fail cancel")===-1){
            that.getSetvelTime() 
          }
        }
      })  
  },
  getSetvelTime(){
    var c = 2;
    var that = this;
    that.setData({
      timeFlag:true,
    })
    var intervalId = setInterval(function(){
      c = c-1;
      that.setData({
        setvalText:c,
      })
      if(c==0){
        clearInterval(intervalId);
        that.setData({
          setvalText:2,
          timeFlag:false,
        })
        wx.navigateBack({
          delta: 1,
        }) 
      }
    },1000)
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    wx.redirectTo({     
      url: `/remote-sales/pages/webview/webview?url=${this.data.pageObj.backUrl || ''}`
    })  
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  }
})
