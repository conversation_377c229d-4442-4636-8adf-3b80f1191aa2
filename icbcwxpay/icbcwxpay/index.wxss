.wxpaycont{
  background-color: #ffffff;
  padding: 30rpx;
  width: 100%;
  box-sizing: border-box;
  height: 100vh;
}
.tips{
  font-size: 28rpx;
  line-height: 44rpx;
  color: #848484;
  width: 100%;
  text-align: center;
  margin-bottom: 12rpx;
  margin-top: 100rpx;
}
.priceText{
  font-size: 48rpx;
  line-height: 36px;
  text-align: center;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: baseline;
}
.line{
  width: 100%;
  height: 2rpx;
  box-sizing: border-box;
  background-color: #f3f3f3;
  margin-top: 30rpx;
}
.priceText text{
  font-size: 24rpx;
  margin-right: 8rpx;
}
.wxpay-detail{
  margin: 12px 0;
}
.wxpay-detail-item{
  align-items: center;
  line-height:22px;
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
}
.wxpay-detail-item .label{
  font-size: 28rpx;
  text-align: left;
  color: #848484;
}
.wxpay-detail-item .text{
  color: #1f1f1f;
  font-size: 28rpx;
}
.wxpayButton{
  width: 168px;
  margin: 16px auto;
  color: white;
  background-color: #000000;
  text-align: center;
  height: 44px;
  line-height: 44px;
}
.dialogMask{
  width: 100%;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  display: none;
}
.dialogContent{
  width: 60%;
  height: 80px;
  background-color: #fff;
  top:50%;
  left: 20%;
  margin-top: -80rpx;
  position: absolute;
  text-align: center;
  line-height: 80px;
  color: #666;
}