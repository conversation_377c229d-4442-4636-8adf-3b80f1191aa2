"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var Notification=function(){function n(){this.subscribers={}}return n.prototype.publish=function(n,e){n=this.subscribers[n];if(n)for(var t=0,i=n.slice();t<i.length;t++){var r=i[t];try{r.call(null,e)}catch(n){}}},n.prototype.subscribe=function(n,e){"function"==typeof e&&(this.subscribers[n]||(this.subscribers[n]=[]),this.subscribers[n].push(e))},n.prototype.unsubscribe=function(n,e){var t=this.subscribers[n];t&&(t.splice(t.indexOf(e),1),0===t.length&&delete this.subscribers[n])},n}(),EMPTY="";function interpret(n,t){return n?"string"==typeof n?n:n.reduce(function(n,e){return n.concat([_eval(e,t)])},[]).join(""):EMPTY}function _eval(t,i){if(i=i||{},"string"==typeof t)return t;if(t[2]&&"object"==typeof t[2]){var n=Object.keys(t[2]).reduce(function(n,e){return n[e]=interpret(t[2][e],i),n},{}),e=n[i[0]],r=i[t[0]];return void 0!==r?n[r.toString()]||n.other||EMPTY:e||n.other||EMPTY}return"object"==typeof t&&0<t.length?getParams(t[0].split("."),i,0):""}function getParams(n,e,t){if(!e||!n||n.length<=0)return"";e=e[n[t=void 0===t?0:t]];return"string"==typeof e?e:"number"==typeof e?e.toString():e?getParams(n,e,++t):"{"+n.join(".")+"}"}function lookUpASTFallback(n,e,t){e=n[e];if(!e)return t;e=e[t];return e||t}function lookUpAST(n,e,t,i){t=e[t];if(!t)return lookUpASTFallback(e,i,n);t=t[n];return t||lookUpASTFallback(e,i,n)}(exports.Locale||(exports.Locale={})).default="en-US";var LOCALE_CHANGE_NOTIFICATION_NAME="localeChange",notification=new Notification,I18nRuntimeBase=function(){function n(n,e,t){if(void 0===n&&(n={}),void 0===e&&(e=exports.Locale.default),void 0===t&&(t=exports.Locale.default),this.translations=n,this.currentLocale=e,this.fallbackLocale=t,!this.translations)throw new Error("[i18n] translations should be specified before using I18n")}return n.prototype.lookUpAST=function(n){return lookUpAST(n,this.translations,this.currentLocale,this.fallbackLocale)},n.prototype.getString=function(n,e){return interpret(this.lookUpAST(n),e)},n.prototype.setLocale=function(n){this.currentLocale=n,notification.publish(LOCALE_CHANGE_NOTIFICATION_NAME,this.currentLocale)},n.prototype.getLocale=function(){return this.currentLocale},n.prototype.loadTranslations=function(n){n&&"object"==typeof n&&(this.translations=n)},n.prototype.t=function(n,e){return this.getString(n,e)},n.prototype.getFallbackLocale=function(){return this.fallbackLocale},n.prototype.onLocaleChange=function(n){return notification.subscribe(LOCALE_CHANGE_NOTIFICATION_NAME,n),{off:function(){notification.unsubscribe(LOCALE_CHANGE_NOTIFICATION_NAME,n)}}},n}(),innerGlobals={i18nInstance:null};try{var locales=require("../../../i18n/locales.js");locales&&locales.translations&&(innerGlobals.i18nInstance=new I18nRuntimeBase(locales.translations,locales.defaultLocale,locales.fallbackLocale))}catch(n){}function initI18n(n,e){return innerGlobals.i18nInstance=new I18nRuntimeBase(n.translations,n.defaultLocale,n.fallbackLocale),innerGlobals.i18nInstance}function getI18nInstance(){return innerGlobals.i18nInstance}var CURRENT_LOCALE_KEY="$_locale",LOCALE_CHANGE_HANDLER_NAME="$_localeChange";function I18nPage(i){if(i=i||{},!innerGlobals.i18nInstance)throw new Error("[i18n] ensure run initI18n() in app.js before using I18n library");if(i.data&&i.data.hasOwnProperty(CURRENT_LOCALE_KEY))throw new Error("[i18n] conflict data field ["+CURRENT_LOCALE_KEY+"] with I18n library");if(i.hasOwnProperty(LOCALE_CHANGE_HANDLER_NAME))throw new Error("[i18n] conflict page method ["+LOCALE_CHANGE_HANDLER_NAME+"] with I18n library");var n=((n={})[LOCALE_CHANGE_HANDLER_NAME]=function(n){var e;this.setData(((e={})[CURRENT_LOCALE_KEY]=n,e))},n.onLoad=function(){for(var n,e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.setData(((n={})[CURRENT_LOCALE_KEY]=innerGlobals.i18nInstance&&innerGlobals.i18nInstance.currentLocale,n)),notification.subscribe(LOCALE_CHANGE_NOTIFICATION_NAME,this[LOCALE_CHANGE_HANDLER_NAME]),"function"==typeof i.onLoad&&i.onLoad.apply(this,e)},n.onUnload=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];"function"==typeof i.onUnload&&i.onUnload.apply(this,n),notification.unsubscribe(LOCALE_CHANGE_NOTIFICATION_NAME,this[LOCALE_CHANGE_HANDLER_NAME])},n.t=function(n,e){if(!innerGlobals.i18nInstance)throw new Error("[i18n] ensure run initI18n() in app.js before using I18n library");return innerGlobals.i18nInstance.getString(n,e)},n.getLocale=function(){if(!innerGlobals.i18nInstance)throw new Error("[i18n] ensure run initI18n() in app.js before using I18n library");return innerGlobals.i18nInstance.getLocale()},n.setLocale=function(n){if(!innerGlobals.i18nInstance)throw new Error("[i18n] ensure run initI18n() in app.js before using I18n library");innerGlobals.i18nInstance.setLocale(n)},n.onLocaleChange=function(n){if(!innerGlobals.i18nInstance)throw new Error("[i18n] ensure run initI18n() in app.js before using I18n library");return innerGlobals.i18nInstance.onLocaleChange(n)},n);return Page(Object.assign({},i,n))}var I18n=Behavior({lifetimes:{created:function(){var t=this;this[LOCALE_CHANGE_HANDLER_NAME]=function(n){var e;t.setData(((e={})[CURRENT_LOCALE_KEY]=n,e))}},attached:function(){var n;if(!innerGlobals.i18nInstance)throw new Error("[i18n] ensure run initI18n() in app.js before using I18n library");this.setData(((n={})[CURRENT_LOCALE_KEY]=innerGlobals.i18nInstance.currentLocale,n)),notification.subscribe(LOCALE_CHANGE_NOTIFICATION_NAME,this[LOCALE_CHANGE_HANDLER_NAME])},detached:function(){notification.unsubscribe(LOCALE_CHANGE_NOTIFICATION_NAME,this[LOCALE_CHANGE_HANDLER_NAME])}},methods:{t:function(n,e){if(!innerGlobals.i18nInstance)throw new Error("[i18n] ensure run initI18n() in app.js before using I18n library");return innerGlobals.i18nInstance.getString(n,e)},setLocale:function(n){if(!innerGlobals.i18nInstance)throw new Error("[i18n] ensure run initI18n() in app.js before using I18n library");return innerGlobals.i18nInstance.setLocale(n)},getLocale:function(){if(!innerGlobals.i18nInstance)throw new Error("[i18n] ensure run initI18n() in app.js before using I18n library");return innerGlobals.i18nInstance.getLocale()},onLocaleChange:function(n){if(!innerGlobals.i18nInstance)throw new Error("[i18n] ensure run initI18n() in app.js before using I18n library");return innerGlobals.i18nInstance.onLocaleChange(n)}}});exports.I18nRuntimeBase=I18nRuntimeBase,exports.initI18n=initI18n,exports.getI18nInstance=getI18nInstance,exports.I18nPage=I18nPage,exports.I18n=I18n;
