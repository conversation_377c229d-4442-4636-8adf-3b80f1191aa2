import { promisify } from '../utils/promisify'
import request from "./request"
const App = getApp();

/**
 * 微信端登录
 * @param {*} data 
 * @returns 
 */
export const miniLogin = async () => {
  const { code } = await promisify(wx.login)()
  let data = {
    code
  }
  return request('/api/wap/qylogin/mini-login/cl/2c', { method: 'POST', data })
}

/**
 * 企业微信登录
 * @param {*} data 
 * @returns 
 */
export const wxworkLogin = async () => {
  const { code } = await promisify(wx.qy.login)()
  let data = {
      code
  }
  return request('/api/wap/qylogin/mini-login', { method: 'POST', data })
}

/**
 * debug 登录
 * @param {*} query 
 * @returns 
 */
export const debugLogin = async (wwId) => {
  return request(`/qyapp/login/mini-login/debug/${wwId}`, { method: 'POST', data: {
    wwid: wwId
  } })
}

/**
 * login 统一接口
 * @param {*} data
 * @returns
 */
export const login = async data => {
  const { environment } = App.globalData
  return environment == 'wxwork' ? wxworkLogin() : miniLogin()
}

/**
 * 获取登录用户信息统一接口
 * @returns
 */
export const getUserInfo = (query = {}) => {
  return request('/api/wap/auth/getInfo', { method: 'GET', query, setStoreId: false, loading: true }).then(res=>{
    const {frontUserRole, defaultStoreId, stores } = res.data
    const defaultStore = defaultStoreId ? stores.find(item=>item.storeId === defaultStoreId)||stores[0] : stores[0]
    const targetRes = {
      ...res,
      data: {
        ...res.data,
        isRegionManager: frontUserRole === 'REGION_MANAGER',
        isStoreManager: frontUserRole === 'STORE_MANAGER',
        frontRoleEnum: frontUserRole,
        mainStore: defaultStore,
        selectStore: defaultStore,
      }
    }
    return targetRes
  })
}

