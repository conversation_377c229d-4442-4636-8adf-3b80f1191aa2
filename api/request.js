import config from "../siteinfo";
import { login, getUserInfo } from "../api/login";
import { useStore } from "../hooks/index";
const App = getApp()
const defaultOptions = {
  timeout: 60000,
  showLog: false,
};

// Object to url params
const appendUrlParams = (url, obj) => {
  if (!obj || !Object.keys(obj).length) return url;
  let params = "";
  for (const key in obj) {
    obj[key] && (params += `&${key}=${obj[key]}`);
  }
  return `${
    url.includes("?") ? url + params : url + "?" + params.substring(1)
  }`;
};
const request = async (url, options) => {
  const {
    header = {},
    data = {},
    query = {},
    method = "GET",
    setStoreId = true,
    noTip = false,
  } = options;

  // show loading
  if (options.loading) {
    App.showLoading()
  }

  if (setStoreId) {
    switch (method) {
      case "GET":
        query.storeId = useStore.get("caInfo").mainDepart?.id;
        break;
      case "POST":
        data.storeId = useStore.get("caInfo").mainDepart?.id;
        break;
      default:
        break;
    }
  }
  const baseUrl = `${config.baseUrl}${url}`;
  const composeUrl = appendUrlParams(baseUrl, query);
  const Token = wx.getStorageSync("token") || undefined;

  return new Promise((resolve, reject) => {
    wx.request({
      url: composeUrl,
      data: data,
      method: method,
      header: {
        "content-type": "application/json",
        Token,
        ...header,
      },
      ...defaultOptions,
      success: async (res) => {
        if (defaultOptions.showLog) {
          console.log(`request end: ${url}`);
          console.log(`request data:`, data);
        }
        if (res.statusCode != 200 || res.data.code != 200) {
          if (res.data.code == 700) {
            const loginRes = await login()
            wx.setStorageSync("token", loginRes.data);
            if(App.globalData.environment == 'wxwork'){
              await getUserInfo().then(res => {
                wx.setStorageSync('caInfo', res.data)
              })
            }
            const res = await request(url, options);
            resolve(res);
          } else if (res.data.code == 707) {
            wx.reLaunch({
              url: "/pages/access/access",
            });
          } else{
            if(!noTip) {
              wx.showToast({
                title: res.data.message || '服务错误',
                icon: 'none'
              })
            }
            resolve(res.data)
          }
          return false
        }else{
          resolve(res.data);
        }
      },
      fail(err) {
        reject(err);
      },
      complete() {
        if (options.loading) {
          setTimeout(() => {
            App.hideLoading()
          }, 100);
        }
      },
    });
  });
};

/**
 * 网络请求封装
 * @param {*} url
 * @param {*} options header、query、data, method
 */
const Api = async (url, options) => {
  return request(url, options);
};

export default Api;
