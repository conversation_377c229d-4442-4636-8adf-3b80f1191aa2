import request from "./request"

/**
 * 获取素材目录
 * @param {*} data 
 * @returns 
 */
export const queryTopCategory = async () => {
  return request('/api/wap/qySysProduct/queryTopCategory', { method: 'GET', loading: true })
}

/**
 * 根据外部产品二级分类id获取过滤选项/
 * @param {*} data 
 * @returns 
 */
export const queryAllFilterByCategoryId = async (data) => {
  return request('/api/wap/qyExtProduct/queryAllFilterByCategoryId', { method: 'GET', query:data, loading: true })
}

/**
 * 通过过滤选项查询产品数量
 * @param {*} data 
 * @returns 
 */
export const queryProductCountByFilter = async (data) => {
  return request('/api/wap/qyExtProduct/queryProductCountByFilter', { method: 'POST', data, loading: false })
}

/**
 * 获取系统产品
 * @param {*} data 
 * @returns 
 */
export const queryCategoryProduct = async (data) => {
  return request('/api/wap/qySysProduct/queryCategoryProduct', { method: 'POST', data, loading: true })
}

/**
* 获取外部产品
* @param {*} data 
* @returns 
*/
export const queryExtProduct = async (data) => {
  return request('/api/wap/qyExtProduct/queryCategoryProduct', { method: 'POST', data, loading: true })
}

/**
 * 取消收藏系统产品
 * @param {*} data 
 * @returns 
 */
export const disCollectProduct = async (data) => {
  return request('/api/wap/qySysProduct/disCollectProduct', { method: 'POST', data, loading: false })
}

/**
* 收藏系统产品
* @param {*} data 
* @returns 
*/
export const collectProduct = async (data) => {
  return request('/api/wap/qySysProduct/collectProduct', { method: 'POST', data, loading: false })
}

/**
 * 取消收藏外部产品
 * @param {*} data 
 * @returns 
 */
export const disCollectExtProduct = async (data) => {
  return request('/api/wap/qyExtProduct/disCollectProduct', { method: 'POST', data, loading: false })
}

/**
* 收藏外部产品
* @param {*} data 
* @returns 
*/
export const collectExtProduct = async (data) => {
  return request('/api/wap/qyExtProduct/collectProduct', { method: 'POST', data, loading: false })
}

/**
* 保存系统分享记录
* @param {*} data 
* @returns 
*/
export const saveShareRecord = async (data) => {
  return request('/api/wap/qySysProduct/saveShareRecord', { method: 'POST', data, loading: true })
}

/**
* 保存外部分享记录
* @param {*} data 
* @returns 
*/
export const saveExtShareRecord = async (data) => {
  return request('/api/wap/qyExtProduct/saveProductsShareRecord', { method: 'POST', data, loading: true })
}

/**
 * 查询产品的热门搜索
 * @param {*} data 
 * @returns 
 */
export const queryHotSearchHistory = async () => {
	return request("/api/wap/qySysProduct/queryHotSearchHistory", { method: "GET", loading: true });
};

/**
 * 查询当前用户的搜索历史
 * @param {*} data 
 * @returns 
 */
export const querySearchHistory = async () => {
	return request("/api/wap/qySysProduct/querySearchHistory", { method: "GET", loading: true });
};

/**
* 清空当前用户的搜索历史
* @param {*} data 
* @returns 
*/
export const clearSearchHistory = async (data) => {
	return request("/api/wap/qySysProduct/clearSearchHistory", { method: "POST", data, loading: true });
};

/**
* 查询全局搜索中有数据的类
* @param {*} data 
* @returns 
*/
export const queryHaveProductTypeExt = async (data) => {
	return request("/api/wap/qyExtProduct/queryHaveProductType", { method: "POST", data, loading: true });
};

/**
* 查询全局搜索中有数据的类
* @param {*} data 
* @returns 
*/
export const queryHaveProductTypeSys = async (data) => {
	return request("/api/wap/qySysProduct/queryHaveProductType", { method: "POST", data, loading: true });
};

/**
* 查询全局搜索中有数据的类
* @param {*} data 
* @returns 
*/
export const queryExtAndSysHaveProductType = async (data) => {
	return request("/api/wap/qySysProduct/queryExtAndSysHaveProductType", { method: "POST", data, loading: true });
};


/**
* 根据id查询系统素材
* @param {*} data 
* @returns 
*/
export const queryProductByIds = async (data) => {
  return request(`/api/wap/qySysProduct/queryProductByIds`, { method: 'POST', data, loading: true })
}

/**
* 根据id查询系统素材 - 微信端
* @param {*} data 
* @returns 
*/
export const queryProductByIdsWx = async (data) => {
  return request(`/api/wap/sysProduct/queryProductByIds`, { method: 'POST', data, loading: true })
}

/**
* 上传系统产品到素材库
* @param {*} data 
* @returns 
*/
export const uploadProductFile = async (data) => {
  return request(`/api/wap/qySysProduct/uploadProductFile`, { method: 'POST', data, loading: true })
}

/**
* 上传外部产品到素材库
* @param {*} data 
* @returns 
*/
export const uploadExtProductFile = async (data) => {
  return request(`/api/wap/qyExtProduct/uploadProductFile`, { method: 'POST', data, loading: true })
}



/**
 * 根据产品id查询外部产品
 * @param {*} data 
 * @returns 
 */
export const queryProductById = async (data) => {
  return request('/api/wap/qyExtProduct/queryProductById', { method: 'GET',query:data, loading: true })
}

/**
* 根据产品ids查询外部产品
* @param {*} data 
* @returns 
*/
export const queryProductByIdsExt = async (data) => {
  return request(`/api/wap/qyExtProduct/queryProductByIds`, { method: 'POST', data, loading: true })
}

/**
* 根据产品ids查询外部产品-微信端
* @param {*} data 
* @returns 
*/
export const queryProductByIdsExtWx = async (data) => {
  return request(`/api/wap/extProduct/queryProductByIds`, { method: 'POST', data, loading: true })
}